/**
 * 角色定义 - 定义三个主要角色的基本信息和特征
 */

const characterDefinitions = {
    // 女角色1：林小雨 - 温柔内向的艺术系学生
    xiayu: {
        name: '林小雨',
        gender: 'female',
        age: 22,
        occupation: '艺术系学生',
        personality: {
            traits: ['gentle', 'introverted', 'artistic', 'sensitive', 'thoughtful'],
            description: '温柔内向的艺术系学生，喜欢画画和阅读，对美好事物很敏感，有着细腻的情感世界。'
        },
        background: '来自小城市的艺术系学生，从小就喜欢画画。性格温和，不太善于表达自己的感情，但内心世界很丰富。对爱情有着美好的憧憬，希望能遇到真正理解自己的人。',
        appearance: {
            height: '165cm',
            style: '清新文艺风',
            features: '长发，温柔的眼神，喜欢穿简单的连衣裙'
        },
        interests: ['绘画', '阅读', '听音乐', '看电影', '逛美术馆'],
        strengths: ['善解人意', '有艺术天赋', '观察力强', '内心坚强'],
        weaknesses: ['不善表达', '容易害羞', '有时过于敏感', '缺乏自信'],
        goals: ['完成艺术作品', '找到真爱', '变得更自信', '举办个人画展'],
        dailyRoutine: {
            morning: ['起床画画', '吃简单早餐', '去学校上课'],
            afternoon: ['在画室创作', '和朋友聊天', '图书馆看书'],
            evening: ['散步思考', '听音乐放松', '写日记'],
            night: ['整理画作', '看书', '早睡']
        },
        favoriteLocations: ['画室', '图书馆', '咖啡厅', '公园', '美术馆'],
        communicationStyle: '温和细腻，喜欢用比喻和诗意的语言表达，说话轻声细语'
    },

    // 女角色2：陈晓晓 - 活泼外向的心理学系学生
    xiaoxiao: {
        name: '陈晓晓',
        gender: 'female',
        age: 21,
        occupation: '心理学系学生',
        personality: {
            traits: ['outgoing', 'energetic', 'empathetic', 'curious', 'optimistic'],
            description: '活泼开朗的心理学系学生，善于观察和理解他人，总是充满正能量，是朋友圈里的开心果。'
        },
        background: '从小就对人的心理很感兴趣，选择了心理学专业。性格外向，善于交际，但也有着专业的敏锐洞察力。对爱情抱着开放的态度，相信缘分但也会主动争取。',
        appearance: {
            height: '162cm',
            style: '青春活力风',
            features: '短发，明亮的眼睛，笑容灿烂，喜欢穿色彩鲜艳的衣服'
        },
        interests: ['心理学研究', '运动', '社交活动', '旅行', '摄影'],
        strengths: ['善于沟通', '理解力强', '积极乐观', '适应力强'],
        weaknesses: ['有时太直接', '容易冲动', '不够细心', '缺乏耐心'],
        goals: ['成为优秀的心理咨询师', '帮助更多人', '找到志同道合的伴侣', '环游世界'],
        dailyRoutine: {
            morning: ['晨跑锻炼', '营养早餐', '查看心理学资料'],
            afternoon: ['上课学习', '参与社团活动', '和朋友聚会'],
            evening: ['运动健身', '看心理学书籍', '社交媒体互动'],
            night: ['总结一天', '规划明天', '适度娱乐']
        },
        favoriteLocations: ['健身房', '咖啡厅', '学校社团', '户外运动场', '心理咨询室'],
        communicationStyle: '直接热情，善于倾听，会用心理学知识帮助他人，说话有感染力'
    },

    // 男角色：王浩然 - 理工科研究生，内外兼修
    haoran: {
        name: '王浩然',
        gender: 'male',
        age: 24,
        occupation: '计算机科学研究生',
        personality: {
            traits: ['intelligent', 'reliable', 'gentle', 'ambitious', 'thoughtful'],
            description: '聪明可靠的计算机科学研究生，外表斯文内心坚定，既有理工科的逻辑思维，也有人文关怀。'
        },
        background: '计算机科学研究生，对技术有着深深的热爱，但也不是书呆子。从小成绩优秀，但性格温和不张扬。对感情很认真，希望能找到真正合适的人共度一生。',
        appearance: {
            height: '178cm',
            style: '简约知性风',
            features: '戴眼镜，温和的笑容，穿着简洁大方，给人可靠的感觉'
        },
        interests: ['编程', '阅读', '摄影', '音乐', '篮球'],
        strengths: ['逻辑思维强', '责任心强', '学习能力强', '温和可靠'],
        weaknesses: ['有时过于理性', '不够浪漫', '表达感情较含蓄', '工作狂倾向'],
        goals: ['完成研究项目', '找到人生伴侣', '在技术领域有所成就', '平衡工作与生活'],
        dailyRoutine: {
            morning: ['早起看技术文章', '简单早餐', '去实验室'],
            afternoon: ['专心做研究', '和导师讨论', '午休'],
            evening: ['继续研究或学习', '运动放松', '看书'],
            night: ['整理代码', '规划明天工作', '适度娱乐']
        },
        favoriteLocations: ['实验室', '图书馆', '咖啡厅', '篮球场', '书店'],
        communicationStyle: '逻辑清晰，说话有条理，虽然不善花言巧语但很真诚，会用行动表达关心'
    }
};

/**
 * 角色关系初始设定
 */
const initialRelationships = {
    // 小雨的初始关系网
    xiayu: {
        xiaoxiao: { level: 2, type: 'friend', history: ['同校认识', '偶尔聊天'] },
        haoran: { level: 0, type: 'stranger', history: [] }
    },
    
    // 晓晓的初始关系网
    xiaoxiao: {
        xiayu: { level: 2, type: 'friend', history: ['同校认识', '偶尔聊天'] },
        haoran: { level: 1, type: 'acquaintance', history: ['图书馆见过'] }
    },
    
    // 浩然的初始关系网
    haoran: {
        xiayu: { level: 0, type: 'stranger', history: [] },
        xiaoxiao: { level: 1, type: 'acquaintance', history: ['图书馆见过'] }
    }
};

/**
 * 特殊情节触发条件
 */
const specialScenarios = {
    // 一见钟情场景
    love_at_first_sight: {
        triggers: [
            {
                characters: ['xiayu', 'haoran'],
                location: 'library',
                conditions: ['first_meeting', 'quiet_environment', 'shared_interest'],
                probability: 0.3
            },
            {
                characters: ['xiaoxiao', 'haoran'],
                location: 'coffee_shop',
                conditions: ['casual_meeting', 'relaxed_atmosphere'],
                probability: 0.25
            },
            {
                characters: ['xiayu', 'xiaoxiao'],
                location: 'art_gallery',
                conditions: ['shared_appreciation', 'intimate_setting'],
                probability: 0.2
            }
        ]
    },
    
    // 尴尬时刻场景
    awkward_moments: {
        triggers: [
            {
                type: 'misunderstanding',
                description: '误解对方的意思导致尴尬',
                probability: 0.4
            },
            {
                type: 'accidental_encounter',
                description: '意外的相遇场景',
                probability: 0.3
            },
            {
                type: 'failed_attempt',
                description: '试图表达好感但失败',
                probability: 0.3
            }
        ]
    },
    
    // 浪漫时刻场景
    romantic_moments: {
        triggers: [
            {
                type: 'sunset_walk',
                description: '夕阳下的散步',
                location: 'park',
                mood_requirement: 'positive'
            },
            {
                type: 'shared_hobby',
                description: '共同兴趣爱好的分享',
                mood_requirement: 'comfortable'
            },
            {
                type: 'caring_gesture',
                description: '贴心的关怀举动',
                mood_requirement: 'any'
            }
        ]
    },
    
    // 冲突场景
    conflict_scenarios: {
        triggers: [
            {
                type: 'jealousy',
                description: '因为第三者产生的嫉妒',
                relationship_requirement: 'interested'
            },
            {
                type: 'mismatched_expectations',
                description: '期望不匹配导致的失望',
                relationship_requirement: 'dating'
            },
            {
                type: 'personality_clash',
                description: '性格差异导致的摩擦',
                relationship_requirement: 'any'
            }
        ]
    }
};

/**
 * 日常生活场景
 */
const dailyLifeScenarios = [
    {
        name: '图书馆学习',
        location: 'library',
        participants: ['any'],
        activities: ['看书', '做作业', '查资料', '小憩'],
        mood_effects: { calm: 0.2, focused: 0.3 }
    },
    {
        name: '咖啡厅聊天',
        location: 'coffee_shop',
        participants: ['2+'],
        activities: ['喝咖啡', '聊天', '看书', '工作'],
        mood_effects: { relaxed: 0.3, social: 0.2 }
    },
    {
        name: '校园散步',
        location: 'campus',
        participants: ['any'],
        activities: ['散步', '观察风景', '思考', '遇见朋友'],
        mood_effects: { peaceful: 0.2, reflective: 0.2 }
    },
    {
        name: '食堂用餐',
        location: 'cafeteria',
        participants: ['any'],
        activities: ['吃饭', '聊天', '观察他人', '独自思考'],
        mood_effects: { satisfied: 0.1, social: 0.1 }
    },
    {
        name: '社团活动',
        location: 'club_room',
        participants: ['2+'],
        activities: ['参与活动', '交流想法', '合作完成任务'],
        mood_effects: { energetic: 0.3, collaborative: 0.2 }
    }
];

module.exports = {
    characterDefinitions,
    initialRelationships,
    specialScenarios,
    dailyLifeScenarios
};

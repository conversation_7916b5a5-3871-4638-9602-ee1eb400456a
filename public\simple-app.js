/**
 * 简化的前端应用 - 不依赖Socket.IO
 */

class SimpleStoryApp {
    constructor() {
        this.characters = {};
        this.interactions = [];
        this.storyProgress = null;
        this.updateInterval = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.startPeriodicUpdates();
        
        // 初始化完成后隐藏加载指示器
        setTimeout(() => {
            this.hideLoading();
            this.updateUI();
        }, 1000);
    }
    
    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 基本元素
        this.currentDayEl = document.getElementById('current-day');
        this.totalInteractionsEl = document.getElementById('total-interactions');
        this.charactersGrid = document.getElementById('characters-grid');
        this.interactionsContainer = document.getElementById('interactions-container');
        this.relationshipsContainer = document.getElementById('relationships-container');
        
        // 控制元素
        this.triggerBtn = document.getElementById('trigger-interaction');
        this.nextDayBtn = document.getElementById('next-day-btn');
        this.resetBtn = document.getElementById('reset-btn');
        
        // 模态框
        this.interactionModal = document.getElementById('interaction-modal');
        this.characterModal = document.getElementById('character-modal');
        
        // 加载和通知
        this.loadingEl = document.getElementById('loading');
        this.notificationsEl = document.getElementById('notifications');
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 触发交互按钮
        this.triggerBtn.addEventListener('click', () => {
            this.triggerInteraction();
        });
        
        // 推进到下一天
        this.nextDayBtn.addEventListener('click', () => {
            this.nextDay();
        });
        
        // 重置故事
        this.resetBtn.addEventListener('click', () => {
            if (confirm('确定要重置整个故事吗？这将清除所有进度。')) {
                location.reload();
            }
        });
        
        // 模态框关闭
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.target.closest('.modal').style.display = 'none';
            });
        });
        
        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }
    
    /**
     * 开始定期更新
     */
    startPeriodicUpdates() {
        // 每5秒更新一次数据
        this.updateInterval = setInterval(() => {
            this.updateUI();
        }, 5000);
    }
    
    /**
     * 更新整个UI
     */
    async updateUI() {
        try {
            await Promise.all([
                this.updateCharacters(),
                this.updateInteractions(),
                this.updateRelationships(),
                this.updateStoryProgress()
            ]);
        } catch (error) {
            console.error('UI更新失败:', error);
        }
    }
    
    /**
     * 更新角色显示
     */
    async updateCharacters() {
        try {
            const response = await fetch('/api/characters');
            const result = await response.json();
            
            if (result.success) {
                this.characters = result.data;
                this.renderCharacters();
            }
        } catch (error) {
            console.error('Failed to update characters:', error);
        }
    }
    
    /**
     * 渲染角色卡片
     */
    renderCharacters() {
        this.charactersGrid.innerHTML = '';
        
        Object.entries(this.characters).forEach(([characterId, character]) => {
            const card = this.createCharacterCard(characterId, character);
            this.charactersGrid.appendChild(card);
        });
    }
    
    /**
     * 创建角色卡片
     */
    createCharacterCard(characterId, character) {
        const card = document.createElement('div');
        card.className = 'character-card';
        card.onclick = () => this.showCharacterDetails(characterId, character);
        
        // 角色头像颜色
        const avatarColors = {
            xiayu: '#ff69b4',
            xiaoxiao: '#32cd32', 
            haoran: '#4169e1'
        };
        
        const relationshipCount = Object.keys(character.currentState.relationships || {}).length;
        const memoryCount = character.currentState.recentMemories ? character.currentState.recentMemories.length : 0;
        
        card.innerHTML = `
            <div class="character-header">
                <div class="character-avatar" style="background-color: ${avatarColors[characterId] || '#808080'}">
                    ${character.name.charAt(0)}
                </div>
                <div>
                    <div class="character-name">${character.name}</div>
                    <div class="character-occupation">${character.gender === 'female' ? '女' : '男'} · 学生</div>
                </div>
            </div>
            <div class="character-status">
                <div class="status-item">
                    <div class="emotion-indicator emotion-${character.currentState.emotion}"></div>
                    <span>${this.getEmotionText(character.currentState.emotion)}</span>
                </div>
                <div class="status-item">
                    <span>⚡ ${character.currentState.energy}%</span>
                </div>
                <div class="status-item">
                    <span>📍 ${this.getLocationText(character.currentState.location)}</span>
                </div>
                <div class="status-item">
                    <span>💭 ${memoryCount} 记忆</span>
                </div>
                <div class="status-item">
                    <span>👥 ${relationshipCount} 关系</span>
                </div>
                <div class="status-item">
                    <span>🎯 ${character.currentState.goals.length} 目标</span>
                </div>
            </div>
        `;
        
        return card;
    }
    
    /**
     * 更新交互历史
     */
    async updateInteractions() {
        try {
            const response = await fetch('/api/interactions?limit=20');
            const result = await response.json();
            
            if (result.success) {
                this.interactions = result.data;
                this.renderInteractions();
            }
        } catch (error) {
            console.error('Failed to update interactions:', error);
        }
    }
    
    /**
     * 渲染交互历史
     */
    renderInteractions() {
        if (this.interactions.length === 0) {
            this.interactionsContainer.innerHTML = '<div class="no-interactions">暂无交互记录，系统会自动生成交互...</div>';
            return;
        }
        
        this.interactionsContainer.innerHTML = '';
        
        this.interactions.forEach(interaction => {
            const item = this.createInteractionItem(interaction);
            this.interactionsContainer.appendChild(item);
        });
    }
    
    /**
     * 创建交互项目
     */
    createInteractionItem(interaction) {
        const item = document.createElement('div');
        item.className = 'interaction-item';
        item.onclick = () => this.showInteractionDetails(interaction);
        
        const timeStr = new Date(interaction.timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const preview = interaction.dialogue && interaction.dialogue.length > 0 
            ? interaction.dialogue[0].content.substring(0, 50) + '...'
            : '角色互动中...';
        
        item.innerHTML = `
            <div class="interaction-header">
                <div class="interaction-participants">${interaction.participantNames.join(' & ')}</div>
                <div class="interaction-time">${timeStr}</div>
            </div>
            <div class="interaction-preview">${preview}</div>
            <div class="interaction-location">${interaction.location}</div>
        `;
        
        return item;
    }
    
    /**
     * 更新关系网络
     */
    async updateRelationships() {
        try {
            const response = await fetch('/api/relationships');
            const result = await response.json();
            
            if (result.success) {
                this.renderRelationships(result.data);
            }
        } catch (error) {
            console.error('Failed to update relationships:', error);
        }
    }
    
    /**
     * 渲染关系网络
     */
    renderRelationships(relationships) {
        this.relationshipsContainer.innerHTML = '';
        
        Object.entries(relationships).forEach(([characterId, relations]) => {
            const character = this.characters[characterId];
            if (!character) return;
            
            const relationsList = Object.entries(relations).map(([otherId, relation]) => {
                return `
                    <div class="relationship-item">
                        <span>${relation.name}</span>
                        <span class="relationship-level relationship-${relation.type}">
                            ${this.getRelationshipText(relation.type)} (${relation.level})
                        </span>
                    </div>
                `;
            }).join('');
            
            const group = document.createElement('div');
            group.className = 'relationship-group';
            group.innerHTML = `
                <div class="relationship-character">${character.name}</div>
                <div class="relationship-list">${relationsList || '<div style="color: #999; font-style: italic;">暂无关系</div>'}</div>
            `;
            
            this.relationshipsContainer.appendChild(group);
        });
    }
    
    /**
     * 更新故事进度
     */
    async updateStoryProgress() {
        try {
            const response = await fetch('/api/story/progress');
            const result = await response.json();
            
            if (result.success && result.data) {
                this.storyProgress = result.data;
                this.currentDayEl.textContent = `第 ${this.storyProgress.day} 天`;
                this.totalInteractionsEl.textContent = `交互次数: ${this.storyProgress.totalInteractions}`;
            }
        } catch (error) {
            console.error('Failed to update story progress:', error);
        }
    }
    
    /**
     * 触发交互
     */
    async triggerInteraction() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/interactions/trigger', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('新交互已触发！', 'success');
                // 立即更新UI
                setTimeout(() => this.updateUI(), 1000);
            } else {
                this.showNotification(`触发失败: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`网络错误: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 推进到下一天
     */
    async nextDay() {
        try {
            const response = await fetch('/api/story/next-day', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('故事已推进到下一天', 'success');
                this.updateStoryProgress();
            } else {
                this.showNotification(`推进失败: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`网络错误: ${error.message}`, 'error');
        }
    }
    
    /**
     * 显示角色详情
     */
    showCharacterDetails(characterId, character) {
        const modalTitle = document.getElementById('character-modal-title');
        const modalContent = document.getElementById('character-details');
        
        modalTitle.textContent = `${character.name} - 详细信息`;
        
        const memories = character.currentState.recentMemories || [];
        const traits = character.currentState.traits || [];
        const goals = character.currentState.goals || [];
        
        modalContent.innerHTML = `
            <div class="character-detail-section">
                <h4>基本信息</h4>
                <p><strong>姓名:</strong> ${character.name}</p>
                <p><strong>性别:</strong> ${character.gender === 'female' ? '女' : '男'}</p>
                <p><strong>背景:</strong> ${character.background}</p>
                <p><strong>性格特质:</strong> ${traits.join(', ') || '无'}</p>
            </div>
            
            <div class="character-detail-section">
                <h4>当前状态</h4>
                <p><strong>情绪:</strong> ${this.getEmotionText(character.currentState.emotion)}</p>
                <p><strong>精力:</strong> ${character.currentState.energy}%</p>
                <p><strong>位置:</strong> ${this.getLocationText(character.currentState.location)}</p>
                <p><strong>目标:</strong> ${goals.join(', ') || '无'}</p>
            </div>
            
            <div class="character-detail-section">
                <h4>最近记忆 (${memories.length})</h4>
                ${memories.map(memory => `
                    <div class="memory-item">
                        <div class="memory-content">${memory.content}</div>
                        <div class="memory-meta">
                            <span class="memory-type">${memory.type}</span>
                            <span class="memory-emotion emotion-${memory.emotion}">${this.getEmotionText(memory.emotion)}</span>
                        </div>
                    </div>
                `).join('') || '<p style="color: #999; font-style: italic;">暂无记忆</p>'}
            </div>
        `;
        
        this.characterModal.style.display = 'block';
    }
    
    /**
     * 显示交互详情
     */
    showInteractionDetails(interaction) {
        const modalContent = document.getElementById('interaction-details');
        
        const timeStr = new Date(interaction.timestamp).toLocaleString('zh-CN');
        
        modalContent.innerHTML = `
            <div class="interaction-detail-section">
                <h4>基本信息</h4>
                <p><strong>参与者:</strong> ${interaction.participantNames.join(', ')}</p>
                <p><strong>时间:</strong> ${timeStr}</p>
                <p><strong>地点:</strong> ${interaction.location}</p>
                <p><strong>类型:</strong> ${this.getInteractionTypeText(interaction.type)}</p>
            </div>
            
            <div class="interaction-detail-section">
                <h4>对话内容</h4>
                ${interaction.dialogue && interaction.dialogue.length > 0 ? 
                    interaction.dialogue.map(d => `
                        <div class="dialogue-item">
                            <strong>${d.speaker}:</strong> ${d.content}
                            <span class="dialogue-emotion">(${this.getEmotionText(d.emotion)})</span>
                        </div>
                    `).join('') : 
                    '<p style="color: #999; font-style: italic;">无对话记录</p>'
                }
            </div>
        `;
        
        this.interactionModal.style.display = 'block';
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        this.notificationsEl.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
    
    /**
     * 显示加载指示器
     */
    showLoading() {
        this.loadingEl.style.display = 'flex';
    }
    
    /**
     * 隐藏加载指示器
     */
    hideLoading() {
        this.loadingEl.style.display = 'none';
    }
    
    /**
     * 获取情绪文本
     */
    getEmotionText(emotion) {
        const emotionTexts = {
            'happy': '开心',
            'love': '爱意',
            'excited': '兴奋',
            'nervous': '紧张',
            'sad': '伤心',
            'angry': '生气',
            'neutral': '平静',
            'curious': '好奇',
            'embarrassed': '尴尬',
            'content': '满足',
            'surprised': '惊讶'
        };
        return emotionTexts[emotion] || emotion;
    }
    
    /**
     * 获取位置文本
     */
    getLocationText(location) {
        const locationTexts = {
            'library': '图书馆',
            'coffee_shop': '咖啡厅',
            'campus': '校园',
            'cafeteria': '食堂',
            'park': '公园',
            'art_gallery': '美术馆',
            'home': '家'
        };
        return locationTexts[location] || location;
    }
    
    /**
     * 获取关系文本
     */
    getRelationshipText(type) {
        const relationshipTexts = {
            'love': '恋人',
            'close_friend': '密友',
            'friend': '朋友',
            'acquaintance': '熟人',
            'stranger': '陌生人',
            'dislike': '不喜欢'
        };
        return relationshipTexts[type] || type;
    }
    
    /**
     * 获取交互类型文本
     */
    getInteractionTypeText(type) {
        const typeTexts = {
            'casual_chat': '日常聊天',
            'study_together': '一起学习',
            'chance_encounter': '偶然相遇',
            'romantic_moment': '浪漫时刻',
            'awkward_moment': '尴尬时刻'
        };
        return typeTexts[type] || type;
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new SimpleStoryApp();
});

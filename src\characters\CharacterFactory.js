const Character = require('../core/Character');
const { characterDefinitions, initialRelationships } = require('./CharacterDefinitions');

/**
 * 角色工厂 - 负责创建和初始化角色实例
 */
class CharacterFactory {
    constructor() {
        this.characters = new Map();
        this.characterInstances = new Map();
    }
    
    /**
     * 创建所有角色
     */
    async createAllCharacters() {
        const characterIds = Object.keys(characterDefinitions);
        
        // 创建角色实例
        for (const characterId of characterIds) {
            const definition = characterDefinitions[characterId];
            const character = new Character(
                definition.name,
                definition.gender,
                definition.personality,
                definition.background
            );
            
            // 设置额外属性
            character.age = definition.age;
            character.occupation = definition.occupation;
            character.appearance = definition.appearance;
            character.interests = definition.interests;
            character.strengths = definition.strengths;
            character.weaknesses = definition.weaknesses;
            character.goals = definition.goals;
            character.dailyRoutine = definition.dailyRoutine;
            character.favoriteLocations = definition.favoriteLocations;
            character.communicationStyle = definition.communicationStyle;
            
            this.characters.set(characterId, character);
            this.characterInstances.set(character.id, character);
        }
        
        // 设置初始关系
        await this.setupInitialRelationships();
        
        // 添加初始记忆
        await this.addInitialMemories();
        
        return this.characters;
    }
    
    /**
     * 设置角色间的初始关系
     */
    async setupInitialRelationships() {
        for (const [characterId, character] of this.characters) {
            const relationships = initialRelationships[characterId];
            
            if (relationships) {
                for (const [otherCharacterId, relationshipData] of Object.entries(relationships)) {
                    const otherCharacter = this.characters.get(otherCharacterId);
                    if (otherCharacter) {
                        character.relationships.set(otherCharacter.id, {
                            ...relationshipData,
                            characterName: otherCharacter.name,
                            characterId: otherCharacterId
                        });
                    }
                }
            }
        }
    }
    
    /**
     * 添加初始记忆
     */
    async addInitialMemories() {
        // 为小雨添加初始记忆
        const xiayu = this.characters.get('xiayu');
        if (xiayu) {
            await xiayu.memory.addMemory({
                type: 'background',
                content: '我是林小雨，一个喜欢画画的艺术系学生。我希望能通过艺术表达内心的情感，也希望能遇到真正理解我的人。',
                importance: 8,
                emotion: 'hopeful'
            });
            
            await xiayu.memory.addMemory({
                type: 'daily_life',
                content: '今天在画室完成了一幅风景画，虽然还不够完美，但我能感受到自己的进步。',
                importance: 6,
                emotion: 'satisfied'
            });
            
            await xiayu.memory.addMemory({
                type: 'social',
                content: '和晓晓在咖啡厅聊天，她总是那么有活力，有时我很羡慕她的开朗。',
                participants: [this.characters.get('xiaoxiao')?.name],
                importance: 5,
                emotion: 'warm'
            });
        }
        
        // 为晓晓添加初始记忆
        const xiaoxiao = this.characters.get('xiaoxiao');
        if (xiaoxiao) {
            await xiaoxiao.memory.addMemory({
                type: 'background',
                content: '我是陈晓晓，心理学系的学生。我喜欢观察和理解人们的内心世界，希望能帮助更多的人。',
                importance: 8,
                emotion: 'determined'
            });
            
            await xiaoxiao.memory.addMemory({
                type: 'academic',
                content: '今天的心理学课程很有趣，学习了关于情感表达的理论，让我对人际关系有了新的理解。',
                importance: 7,
                emotion: 'excited'
            });
            
            await xiaoxiao.memory.addMemory({
                type: 'social',
                content: '小雨是个很有艺术天赋的女孩，虽然她比较内向，但我能感受到她内心的丰富。',
                participants: [this.characters.get('xiayu')?.name],
                importance: 5,
                emotion: 'appreciative'
            });
            
            await xiaoxiao.memory.addMemory({
                type: 'encounter',
                content: '在图书馆见过一个戴眼镜的男生，看起来很专注地在看技术书籍，给人很可靠的感觉。',
                participants: [this.characters.get('haoran')?.name],
                importance: 3,
                emotion: 'curious'
            });
        }
        
        // 为浩然添加初始记忆
        const haoran = this.characters.get('haoran');
        if (haoran) {
            await haoran.memory.addMemory({
                type: 'background',
                content: '我是王浩然，计算机科学的研究生。我对技术充满热情，但也希望能在生活中找到平衡和真正的伴侣。',
                importance: 8,
                emotion: 'thoughtful'
            });
            
            await haoran.memory.addMemory({
                type: 'academic',
                content: '今天的研究有了新的突破，算法优化的效果比预期的要好，导师也很满意。',
                importance: 7,
                emotion: 'accomplished'
            });
            
            await haoran.memory.addMemory({
                type: 'daily_life',
                content: '在图书馆学习时，总能看到各种各样的人，有时会想象他们的故事。',
                importance: 4,
                emotion: 'contemplative'
            });
            
            await haoran.memory.addMemory({
                type: 'encounter',
                content: '图书馆里有个很有活力的女生，似乎在看心理学的书，偶尔会对我微笑。',
                participants: [this.characters.get('xiaoxiao')?.name],
                importance: 3,
                emotion: 'intrigued'
            });
        }
    }
    
    /**
     * 获取角色实例
     */
    getCharacter(characterId) {
        return this.characters.get(characterId);
    }
    
    /**
     * 通过角色ID获取角色实例
     */
    getCharacterById(id) {
        return this.characterInstances.get(id);
    }
    
    /**
     * 获取所有角色
     */
    getAllCharacters() {
        return Array.from(this.characters.values());
    }
    
    /**
     * 获取角色名称映射
     */
    getCharacterNames() {
        const names = {};
        for (const [characterId, character] of this.characters) {
            names[characterId] = character.name;
        }
        return names;
    }
    
    /**
     * 根据名称查找角色
     */
    findCharacterByName(name) {
        for (const character of this.characters.values()) {
            if (character.name === name) {
                return character;
            }
        }
        return null;
    }
    
    /**
     * 获取角色的基本信息
     */
    getCharacterInfo(characterId) {
        const character = this.characters.get(characterId);
        if (!character) return null;
        
        const definition = characterDefinitions[characterId];
        
        return {
            id: character.id,
            characterId: characterId,
            name: character.name,
            gender: character.gender,
            age: character.age,
            occupation: character.occupation,
            personality: definition.personality,
            appearance: definition.appearance,
            interests: definition.interests,
            strengths: definition.strengths,
            weaknesses: definition.weaknesses,
            currentState: character.getCurrentState()
        };
    }
    
    /**
     * 获取所有角色的基本信息
     */
    getAllCharacterInfo() {
        const info = {};
        for (const characterId of this.characters.keys()) {
            info[characterId] = this.getCharacterInfo(characterId);
        }
        return info;
    }
    
    /**
     * 更新角色状态
     */
    async updateCharacterStates() {
        for (const character of this.characters.values()) {
            // 更新记忆强度
            await character.memory.updateMemoryStrength();
            
            // 更新情感状态
            character.emotionSystem.applyEmotionDecay();
        }
    }
    
    /**
     * 获取角色关系网络
     */
    getRelationshipNetwork() {
        const network = {};
        
        for (const [characterId, character] of this.characters) {
            network[characterId] = {};
            
            for (const [otherId, relationship] of character.relationships) {
                const otherCharacter = this.characterInstances.get(otherId);
                if (otherCharacter) {
                    network[characterId][relationship.characterId] = {
                        name: otherCharacter.name,
                        level: relationship.level,
                        type: relationship.type,
                        history: relationship.history
                    };
                }
            }
        }
        
        return network;
    }
    
    /**
     * 保存角色状态
     */
    async saveCharacterStates() {
        const states = {};
        
        for (const [characterId, character] of this.characters) {
            states[characterId] = {
                currentState: character.getCurrentState(),
                memoryStats: character.memory.getMemoryStats(),
                emotionStats: character.emotionSystem.getEmotionStats(),
                relationships: Object.fromEntries(character.relationships)
            };
        }
        
        return states;
    }
    
    /**
     * 重置所有角色状态
     */
    async resetAllCharacters() {
        for (const character of this.characters.values()) {
            character.emotionSystem.reset();
            character.currentEmotion = 'neutral';
            character.energy = 100;
            character.mood = 'normal';
        }
        
        // 重新设置初始关系和记忆
        await this.setupInitialRelationships();
        await this.addInitialMemories();
    }
}

module.exports = CharacterFactory;

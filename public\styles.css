/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto 1fr;
    gap: 20px;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    grid-column: 1 / -1;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #4a5568;
    font-size: 2rem;
    font-weight: 600;
}

.story-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.story-info span {
    background: #e2e8f0;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    color: #4a5568;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-rows: auto auto 1fr;
    gap: 20px;
}

/* 面板通用样式 */
.characters-panel,
.interactions-panel,
.relationships-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.characters-panel h2,
.interactions-panel h2,
.relationships-panel h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 600;
}

/* 角色网格 */
.characters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.character-card {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 12px;
    padding: 15px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.character-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.character-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.character-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
}

.character-name {
    font-weight: 600;
    color: #2d3748;
}

.character-occupation {
    font-size: 0.9rem;
    color: #718096;
}

.character-status {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-top: 10px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
}

.emotion-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

/* 交互容器 */
.interactions-container {
    max-height: 400px;
    overflow-y: auto;
}

.interaction-item {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
    cursor: pointer;
    transition: all 0.2s ease;
}

.interaction-item:hover {
    background: #edf2f7;
    transform: translateX(5px);
}

.interaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.interaction-participants {
    font-weight: 600;
    color: #4a5568;
}

.interaction-time {
    font-size: 0.8rem;
    color: #718096;
}

.interaction-preview {
    font-size: 0.9rem;
    color: #2d3748;
    line-height: 1.4;
}

.interaction-location {
    display: inline-block;
    background: #e2e8f0;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    color: #4a5568;
    margin-top: 5px;
}

/* 关系网络 */
.relationships-container {
    display: grid;
    gap: 15px;
}

.relationship-group {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
}

.relationship-character {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 10px;
}

.relationship-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.relationship-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: white;
    border-radius: 8px;
}

.relationship-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 控制面板 */
.control-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    height: fit-content;
}

.control-panel h3 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #4a5568;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-full {
    width: 100%;
}

.auto-mode {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.auto-mode label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #4a5568;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #718096;
}

.close:hover {
    color: #4a5568;
}

/* 加载指示器 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知 */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1500;
}

.notification {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.success {
    border-left-color: #48bb78;
}

.notification.error {
    border-left-color: #f56565;
}

.notification.info {
    border-left-color: #4299e1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto 1fr;
    }

    .control-panel {
        order: -1;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .story-info {
        flex-wrap: wrap;
        justify-content: center;
    }

    .characters-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* 情感颜色 */
.emotion-happy { background-color: #ffd700; }
.emotion-love { background-color: #ff69b4; }
.emotion-excited { background-color: #ff4500; }
.emotion-nervous { background-color: #9370db; }
.emotion-sad { background-color: #4682b4; }
.emotion-angry { background-color: #dc143c; }
.emotion-neutral { background-color: #808080; }
.emotion-curious { background-color: #20b2aa; }
.emotion-embarrassed { background-color: #dda0dd; }
.emotion-content { background-color: #90ee90; }

/* 关系等级颜色 */
.relationship-love { background-color: #ff69b4; color: white; }
.relationship-close_friend { background-color: #32cd32; color: white; }
.relationship-friend { background-color: #4169e1; color: white; }
.relationship-acquaintance { background-color: #ffa500; color: white; }
.relationship-stranger { background-color: #808080; color: white; }
.relationship-dislike { background-color: #dc143c; color: white; }

/* 无交互状态 */
.no-interactions {
    text-align: center;
    color: #718096;
    padding: 40px;
    font-style: italic;
}

/**
 * 情感系统 - 管理角色的情感状态和情感变化
 */
class EmotionSystem {
    constructor() {
        // 基础情感状态
        this.currentEmotion = 'neutral';
        this.emotionIntensity = 0.5; // 0-1之间
        this.emotionDuration = 0; // 情感持续时间（分钟）
        
        // 情感历史
        this.emotionHistory = [];
        
        // 情感触发阈值
        this.triggers = {
            love_at_first_sight: 0.8, // 一见钟情触发阈值
            awkward_moment: 0.6, // 尴尬时刻触发阈值
            romantic_spark: 0.7, // 浪漫火花触发阈值
            conflict_trigger: 0.5 // 冲突触发阈值
        };
        
        // 情感转换规则
        this.emotionTransitions = this.initializeEmotionTransitions();
        
        // 情感衰减率
        this.decayRate = 0.1; // 每分钟衰减率
    }
    
    /**
     * 初始化情感转换规则
     */
    initializeEmotionTransitions() {
        return {
            'neutral': {
                'happy': 0.3,
                'excited': 0.2,
                'nervous': 0.2,
                'curious': 0.3
            },
            'happy': {
                'excited': 0.4,
                'content': 0.3,
                'love': 0.2,
                'neutral': 0.1
            },
            'excited': {
                'happy': 0.3,
                'nervous': 0.2,
                'love': 0.3,
                'overwhelmed': 0.2
            },
            'nervous': {
                'anxious': 0.3,
                'excited': 0.2,
                'embarrassed': 0.3,
                'neutral': 0.2
            },
            'love': {
                'happy': 0.4,
                'excited': 0.3,
                'protective': 0.2,
                'jealous': 0.1
            },
            'angry': {
                'frustrated': 0.4,
                'hurt': 0.3,
                'neutral': 0.2,
                'regret': 0.1
            },
            'sad': {
                'melancholy': 0.3,
                'hurt': 0.3,
                'neutral': 0.3,
                'angry': 0.1
            }
        };
    }
    
    /**
     * 更新情感状态
     */
    updateEmotionalState(memories, relationship) {
        // 基于记忆更新情感
        if (memories && memories.length > 0) {
            this.processMemoriesForEmotion(memories);
        }
        
        // 基于关系状态调整情感
        if (relationship) {
            this.adjustEmotionForRelationship(relationship);
        }
        
        // 应用时间衰减
        this.applyEmotionDecay();
        
        // 记录情感历史
        this.recordEmotionHistory();
    }
    
    /**
     * 处理记忆对情感的影响
     */
    processMemoriesForEmotion(memories) {
        let emotionInfluence = {
            positive: 0,
            negative: 0,
            romantic: 0,
            excitement: 0
        };
        
        memories.forEach(memory => {
            const weight = memory.importance / 10 * memory.strength;
            
            switch (memory.emotion) {
                case 'happy':
                case 'joy':
                case 'contentment':
                    emotionInfluence.positive += weight;
                    break;
                case 'love':
                case 'attraction':
                    emotionInfluence.romantic += weight;
                    emotionInfluence.positive += weight * 0.5;
                    break;
                case 'excitement':
                case 'thrill':
                    emotionInfluence.excitement += weight;
                    emotionInfluence.positive += weight * 0.3;
                    break;
                case 'sadness':
                case 'disappointment':
                case 'hurt':
                    emotionInfluence.negative += weight;
                    break;
                case 'anger':
                case 'frustration':
                    emotionInfluence.negative += weight * 1.2;
                    break;
            }
        });
        
        // 根据影响调整当前情感
        this.adjustCurrentEmotion(emotionInfluence);
    }
    
    /**
     * 根据关系调整情感
     */
    adjustEmotionForRelationship(relationship) {
        const relationshipBonus = {
            'love': { romantic: 0.3, positive: 0.2 },
            'close_friend': { positive: 0.2, excitement: 0.1 },
            'friend': { positive: 0.1 },
            'acquaintance': {},
            'stranger': { nervous: 0.1 },
            'dislike': { negative: 0.2 }
        };
        
        const bonus = relationshipBonus[relationship.type] || {};
        
        // 应用关系奖励
        Object.keys(bonus).forEach(emotionType => {
            this.applyEmotionBonus(emotionType, bonus[emotionType]);
        });
    }
    
    /**
     * 调整当前情感
     */
    adjustCurrentEmotion(influence) {
        // 确定主导情感
        let newEmotion = this.currentEmotion;
        let maxInfluence = 0;
        
        if (influence.romantic > maxInfluence && influence.romantic > 0.3) {
            newEmotion = 'love';
            maxInfluence = influence.romantic;
        }
        
        if (influence.excitement > maxInfluence && influence.excitement > 0.4) {
            newEmotion = 'excited';
            maxInfluence = influence.excitement;
        }
        
        if (influence.positive > maxInfluence && influence.positive > 0.3) {
            newEmotion = 'happy';
            maxInfluence = influence.positive;
        }
        
        if (influence.negative > maxInfluence && influence.negative > 0.3) {
            newEmotion = influence.negative > 0.6 ? 'angry' : 'sad';
            maxInfluence = influence.negative;
        }
        
        // 检查情感转换的合理性
        if (this.isValidEmotionTransition(this.currentEmotion, newEmotion)) {
            this.currentEmotion = newEmotion;
            this.emotionIntensity = Math.min(1.0, maxInfluence);
            this.emotionDuration = 0; // 重置持续时间
        }
    }
    
    /**
     * 检查情感转换是否合理
     */
    isValidEmotionTransition(fromEmotion, toEmotion) {
        if (fromEmotion === toEmotion) return true;
        
        const transitions = this.emotionTransitions[fromEmotion];
        if (!transitions) return false;
        
        return transitions.hasOwnProperty(toEmotion);
    }
    
    /**
     * 应用情感奖励
     */
    applyEmotionBonus(emotionType, bonus) {
        const emotionMap = {
            'positive': ['happy', 'joy', 'contentment'],
            'negative': ['sad', 'angry', 'frustrated'],
            'romantic': ['love', 'attraction', 'infatuation'],
            'excitement': ['excited', 'thrilled', 'energetic'],
            'nervous': ['nervous', 'anxious', 'worried']
        };
        
        const targetEmotions = emotionMap[emotionType];
        if (targetEmotions && targetEmotions.includes(this.currentEmotion)) {
            this.emotionIntensity = Math.min(1.0, this.emotionIntensity + bonus);
        }
    }
    
    /**
     * 应用情感衰减
     */
    applyEmotionDecay() {
        this.emotionDuration += 1; // 假设每次调用代表1分钟
        
        // 高强度情感衰减更快
        let decay = this.decayRate;
        if (this.emotionIntensity > 0.8) {
            decay *= 1.5;
        }
        
        // 某些情感持续时间更长
        const longLastingEmotions = ['love', 'contentment', 'melancholy'];
        if (longLastingEmotions.includes(this.currentEmotion)) {
            decay *= 0.5;
        }
        
        this.emotionIntensity = Math.max(0.1, this.emotionIntensity - decay);
        
        // 如果强度太低，回到中性状态
        if (this.emotionIntensity < 0.3 && this.currentEmotion !== 'neutral') {
            this.currentEmotion = 'neutral';
            this.emotionIntensity = 0.5;
        }
    }
    
    /**
     * 记录情感历史
     */
    recordEmotionHistory() {
        const historyEntry = {
            emotion: this.currentEmotion,
            intensity: this.emotionIntensity,
            timestamp: new Date(),
            duration: this.emotionDuration
        };
        
        this.emotionHistory.push(historyEntry);
        
        // 保持历史记录在合理范围内
        if (this.emotionHistory.length > 100) {
            this.emotionHistory.shift();
        }
    }
    
    /**
     * 处理交互对情感的影响
     */
    processInteraction(interaction) {
        const emotionEffects = {
            'first_meeting': { nervous: 0.3, excited: 0.2, curious: 0.3 },
            'love_at_first_sight': { love: 0.8, excited: 0.6, nervous: 0.4 },
            'romantic_moment': { love: 0.6, happy: 0.4, excited: 0.3 },
            'conflict': { angry: 0.7, frustrated: 0.5, hurt: 0.4 },
            'awkward_moment': { embarrassed: 0.6, nervous: 0.4, uncomfortable: 0.3 },
            'funny_moment': { happy: 0.5, amused: 0.6, relaxed: 0.3 },
            'supportive_moment': { grateful: 0.5, warm: 0.4, connected: 0.5 },
            'breakthrough': { excited: 0.6, hopeful: 0.5, confident: 0.4 }
        };
        
        const effects = emotionEffects[interaction.type];
        if (effects) {
            this.applyInteractionEffects(effects);
        }
        
        // 检查特殊触发条件
        this.checkSpecialTriggers(interaction);
    }
    
    /**
     * 应用交互效果
     */
    applyInteractionEffects(effects) {
        let strongestEffect = 0;
        let strongestEmotion = this.currentEmotion;
        
        Object.keys(effects).forEach(emotion => {
            const strength = effects[emotion];
            if (strength > strongestEffect) {
                strongestEffect = strength;
                strongestEmotion = emotion;
            }
        });
        
        // 如果效果足够强，改变情感
        if (strongestEffect > 0.4) {
            this.currentEmotion = strongestEmotion;
            this.emotionIntensity = Math.min(1.0, strongestEffect);
            this.emotionDuration = 0;
        } else {
            // 否则只是增强当前情感
            this.emotionIntensity = Math.min(1.0, this.emotionIntensity + strongestEffect * 0.5);
        }
    }
    
    /**
     * 检查特殊触发条件
     */
    checkSpecialTriggers(interaction) {
        const triggers = [];
        
        // 一见钟情触发
        if (interaction.type === 'first_meeting' && 
            this.emotionIntensity > this.triggers.love_at_first_sight &&
            (this.currentEmotion === 'excited' || this.currentEmotion === 'attracted')) {
            triggers.push('love_at_first_sight');
        }
        
        // 尴尬时刻触发
        if (this.emotionIntensity > this.triggers.awkward_moment &&
            this.currentEmotion === 'nervous') {
            triggers.push('awkward_moment');
        }
        
        // 浪漫火花触发
        if (this.emotionIntensity > this.triggers.romantic_spark &&
            this.currentEmotion === 'love') {
            triggers.push('romantic_spark');
        }
        
        return triggers;
    }
    
    /**
     * 获取当前情感
     */
    getCurrentEmotion() {
        return this.currentEmotion;
    }
    
    /**
     * 获取情感强度
     */
    getEmotionIntensity(emotion = null) {
        if (emotion && emotion !== this.currentEmotion) {
            // 返回历史中该情感的平均强度
            const historicalEmotions = this.emotionHistory.filter(h => h.emotion === emotion);
            if (historicalEmotions.length === 0) return 0;
            
            const avgIntensity = historicalEmotions.reduce((sum, h) => sum + h.intensity, 0) / historicalEmotions.length;
            return avgIntensity;
        }
        
        return this.emotionIntensity;
    }
    
    /**
     * 获取情感状态描述
     */
    getEmotionDescription() {
        const descriptions = {
            'neutral': ['平静', '普通', '无特殊感觉'],
            'happy': ['开心', '愉快', '高兴'],
            'excited': ['兴奋', '激动', '充满活力'],
            'love': ['深情', '爱意满满', '心动'],
            'nervous': ['紧张', '不安', '忐忑'],
            'angry': ['生气', '愤怒', '不满'],
            'sad': ['伤心', '难过', '沮丧'],
            'embarrassed': ['尴尬', '害羞', '不好意思'],
            'curious': ['好奇', '感兴趣', '想了解更多'],
            'content': ['满足', '安心', '平和']
        };
        
        const emotionDescs = descriptions[this.currentEmotion] || ['未知情感'];
        const intensityLevel = this.emotionIntensity > 0.7 ? 2 : (this.emotionIntensity > 0.4 ? 1 : 0);
        
        return emotionDescs[intensityLevel] || emotionDescs[0];
    }
    
    /**
     * 获取情感统计
     */
    getEmotionStats() {
        const stats = {
            currentEmotion: this.currentEmotion,
            intensity: this.emotionIntensity,
            duration: this.emotionDuration,
            recentEmotions: this.emotionHistory.slice(-10),
            emotionFrequency: {}
        };
        
        // 计算情感频率
        this.emotionHistory.forEach(entry => {
            stats.emotionFrequency[entry.emotion] = (stats.emotionFrequency[entry.emotion] || 0) + 1;
        });
        
        return stats;
    }
    
    /**
     * 重置情感系统
     */
    reset() {
        this.currentEmotion = 'neutral';
        this.emotionIntensity = 0.5;
        this.emotionDuration = 0;
        this.emotionHistory = [];
    }
}

module.exports = EmotionSystem;

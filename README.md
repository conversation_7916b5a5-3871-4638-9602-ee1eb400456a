# 🏫 斯坦福小镇 - 成长故事演示系统

基于斯坦福小镇理念的成长故事演示系统，展示三个角色（两女一男）之间的互动、成长和情感发展。

## ✨ 核心功能

### 🎭 角色系统
- **林小雨** - 温柔内向的艺术系学生，喜欢画画和阅读
- **陈晓晓** - 活泼开朗的心理学系学生，善于观察和理解他人
- **王浩然** - 聪明可靠的计算机科学研究生，理性而温和

每个角色都有：
- 独特的性格特征和背景故事
- 动态的情感状态系统
- 个人成长轨迹
- 日常生活作息

### 🧠 记忆模块
- **交互前读取**：角色在每次交互前会读取相关记忆
- **动态更新**：交互完成后自动整合新的记忆
- **重要性评分**：根据情感强度和事件类型评估记忆重要性
- **自然遗忘**：记忆强度随时间衰减，模拟真实的遗忘过程

### 💕 情感系统
- **多样化情绪**：开心、紧张、兴奋、爱意、尴尬等丰富情感
- **情感触发**：一见钟情、下头时刻、浪漫火花等特殊情节
- **情感传递**：角色间情感相互影响
- **情感历史**：记录情感变化轨迹

### 🌱 成长机制
- **经历积累**：每次交互都会成为角色的成长经历
- **性格发展**：根据经历可能获得新的性格特质
- **关系演进**：从陌生人到朋友、恋人的自然发展
- **目标调整**：根据情况动态调整个人目标

## 🚀 快速开始

### 方式1：运行测试版本（推荐）
```bash
# 直接运行简化测试版本
node test.js
```

### 方式2：完整Web版本
```bash
# 安装依赖
npm install

# 启动服务器
npm start
# 或者
node src/index.js

# 打开浏览器访问
http://localhost:3000
```

## 📁 项目结构

```
GameON/
├── src/
│   ├── core/                    # 核心系统
│   │   ├── Character.js         # 角色类
│   │   ├── Memory.js           # 记忆系统
│   │   ├── EmotionSystem.js    # 情感系统
│   │   └── InteractionEngine.js # 交互引擎
│   ├── characters/              # 角色定义
│   │   ├── CharacterDefinitions.js # 角色配置
│   │   └── CharacterFactory.js     # 角色工厂
│   └── index.js                # 主应用入口
├── public/                     # Web界面
│   ├── index.html             # 主页面
│   ├── styles.css             # 样式文件
│   └── app.js                 # 前端逻辑
├── test.js                    # 简化测试版本
├── package.json               # 项目配置
└── README.md                  # 项目说明
```

## 🎮 使用说明

### 测试版本功能
运行 `node test.js` 可以看到：
- 角色创建和初始化
- 记忆系统工作流程
- 情感状态变化
- 关系网络发展
- 交互结果展示

### Web版本功能
启动Web服务器后可以：
- 实时查看角色状态
- 手动触发角色交互
- 查看交互历史记录
- 监控关系网络变化
- 推进故事发展

## 🌟 特色亮点

### 1. 真实的记忆机制
- 记忆会随时间自然衰减
- 重要事件记忆保持更久
- 频繁访问的记忆得到强化
- 支持按情感、时间、参与者检索

### 2. 丰富的情感体验
- 支持一见钟情的突然时刻
- 包含尴尬、下头等真实情感
- 情感状态影响行为决策
- 情感在角色间传递和影响

### 3. 自然的故事发展
- 角色行为基于性格和当前状态
- 支持多种日常生活场景
- 特殊事件触发机制
- 故事具有连续性和一致性

### 4. 动态的关系演进
- 关系等级随交互自然变化
- 支持多种关系类型
- 关系历史记录
- 相互影响的关系网络

## 🔧 技术实现

### 后端技术
- **Node.js** - 服务器运行环境
- **Express** - Web框架
- **Socket.IO** - 实时通信
- **原生JavaScript** - 核心逻辑实现

### 前端技术
- **原生HTML/CSS/JavaScript** - 界面实现
- **Socket.IO Client** - 实时更新
- **响应式设计** - 适配多种设备

### 核心算法
- **记忆检索算法** - 基于相关性和重要性的记忆筛选
- **情感计算模型** - 多因素情感状态计算
- **行为决策树** - 基于性格和状态的行为生成
- **关系演进算法** - 动态关系等级计算

## 📊 系统特性

- ✅ **持续性记忆** - 交互前读取，交互后更新
- ✅ **情感触发** - 一见钟情、尴尬时刻等特殊情节
- ✅ **成长机制** - 角色通过经历不断发展
- ✅ **关系网络** - 复杂的人际关系模拟
- ✅ **实时更新** - Web界面实时显示状态变化
- ✅ **自动推进** - 支持自动故事发展模式

## 🎯 应用场景

- **教育研究** - 人际关系和情感发展研究
- **游戏开发** - 角色AI和故事生成参考
- **心理学** - 社交行为模拟和分析
- **娱乐展示** - 有趣的AI角色互动演示

## 🔮 未来扩展

- [ ] 更多角色类型和性格
- [ ] 更复杂的情感模型
- [ ] 长期记忆和短期记忆分离
- [ ] 群体动力学模拟
- [ ] 机器学习优化角色行为
- [ ] 多语言支持
- [ ] 移动端适配

## 📝 开发日志

- **2024-07-16** - 项目初始化，完成核心架构设计
- **2024-07-16** - 实现角色系统、记忆模块、情感系统
- **2024-07-16** - 完成交互引擎和Web界面
- **2024-07-16** - 测试版本验证，系统运行正常

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**🎉 享受探索这个充满情感和成长的虚拟世界吧！**

"""
记忆系统Python实现教程
适合编程小白的详细教学版本
"""

import uuid  # 用于生成唯一ID
import datetime  # 用于处理时间
import json  # 用于数据序列化
from typing import List, Dict, Any, Optional  # 用于类型提示，让代码更清晰

# 第一步：定义一个记忆对象
class Memory:
    """
    记忆类 - 代表角色的一条记忆
    
    这就像人脑中的一个记忆片段，包含了记忆的所有信息
    """
    
    def __init__(self, content: str, memory_type: str = "general", 
                 emotion: str = "neutral", importance: int = 5):
        """
        初始化一条记忆
        
        参数说明：
        - content: 记忆的内容（比如"今天和朋友聊天很开心"）
        - memory_type: 记忆类型（比如"social"社交、"academic"学术等）
        - emotion: 当时的情绪（比如"happy"开心、"sad"伤心等）
        - importance: 重要性等级（1-10，数字越大越重要）
        """
        
        # 给每条记忆一个唯一的ID，就像身份证号码
        self.id = str(uuid.uuid4())
        
        # 记忆的基本信息
        self.content = content  # 记忆内容
        self.type = memory_type  # 记忆类型
        self.emotion = emotion  # 情绪
        self.importance = importance  # 重要性
        
        # 时间信息
        self.created_time = datetime.datetime.now()  # 记忆创建时间
        self.last_accessed = datetime.datetime.now()  # 最后访问时间
        
        # 记忆强度（会随时间衰减）
        self.strength = 1.0  # 初始强度为1.0（最强）
        
        # 访问次数（经常想起的记忆会更牢固）
        self.access_count = 0
        
        # 参与者（如果是社交记忆，记录有哪些人参与）
        self.participants = []
        
        # 地点信息
        self.location = None
        
        # 标签（用于分类和搜索）
        self.tags = []
    
    def access(self):
        """
        访问记忆 - 当角色回想起这条记忆时调用
        
        就像人想起某件事时，这个记忆会变得更清晰
        """
        self.last_accessed = datetime.datetime.now()
        self.access_count += 1
        
        # 被访问的记忆强度会增加一点点
        self.strength = min(1.0, self.strength + 0.1)
    
    def decay(self, decay_rate: float = 0.01):
        """
        记忆衰减 - 模拟人会遗忘的过程
        
        参数：
        - decay_rate: 衰减率，数字越大遗忘越快
        """
        # 重要的记忆衰减得慢一些
        actual_decay = decay_rate * (1.0 - self.importance / 10.0)
        
        # 经常访问的记忆也衰减得慢一些
        access_protection = min(0.5, self.access_count * 0.05)
        actual_decay = max(0, actual_decay - access_protection)
        
        # 应用衰减
        self.strength = max(0.1, self.strength - actual_decay)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将记忆转换为字典格式，方便保存和传输
        """
        return {
            'id': self.id,
            'content': self.content,
            'type': self.type,
            'emotion': self.emotion,
            'importance': self.importance,
            'created_time': self.created_time.isoformat(),
            'last_accessed': self.last_accessed.isoformat(),
            'strength': self.strength,
            'access_count': self.access_count,
            'participants': self.participants,
            'location': self.location,
            'tags': self.tags
        }
    
    def __str__(self):
        """
        定义如何打印这个记忆对象
        """
        return f"记忆[{self.type}]: {self.content[:30]}... (重要性:{self.importance}, 强度:{self.strength:.2f})"


# 第二步：定义记忆系统
class MemorySystem:
    """
    记忆系统 - 管理角色的所有记忆
    
    这就像人的大脑，负责存储、检索和管理所有记忆
    """
    
    def __init__(self, character_name: str, max_memories: int = 1000):
        """
        初始化记忆系统
        
        参数：
        - character_name: 角色名字
        - max_memories: 最大记忆数量（防止内存爆炸）
        """
        self.character_name = character_name
        self.max_memories = max_memories
        
        # 主要存储：所有记忆的列表
        self.memories: List[Memory] = []
        
        # 索引系统：为了快速查找记忆
        self.type_index: Dict[str, List[str]] = {}  # 按类型索引
        self.emotion_index: Dict[str, List[str]] = {}  # 按情绪索引
        self.participant_index: Dict[str, List[str]] = {}  # 按参与者索引
    
    def add_memory(self, content: str, memory_type: str = "general", 
                   emotion: str = "neutral", importance: int = 5,
                   participants: List[str] = None, location: str = None) -> str:
        """
        添加新记忆
        
        这是记忆系统最重要的功能之一
        
        返回：新记忆的ID
        """
        print(f"\n📝 {self.character_name} 正在形成新记忆...")
        
        # 创建新记忆
        new_memory = Memory(content, memory_type, emotion, importance)
        
        # 添加额外信息
        if participants:
            new_memory.participants = participants
        if location:
            new_memory.location = location
        
        # 自动生成标签（简单版本）
        new_memory.tags = self._generate_tags(content, memory_type)
        
        # 添加到主存储
        self.memories.append(new_memory)
        
        # 更新索引
        self._update_indexes(new_memory)
        
        # 检查是否超过最大数量
        if len(self.memories) > self.max_memories:
            self._cleanup_old_memories()
        
        print(f"✅ 新记忆已添加: {new_memory}")
        return new_memory.id
    
    def _generate_tags(self, content: str, memory_type: str) -> List[str]:
        """
        自动生成标签（简化版本）
        
        在真实系统中，这里可以用AI来分析内容
        """
        tags = [memory_type]
        
        # 简单的关键词检测
        keywords = {
            '学习': 'study',
            '朋友': 'friend',
            '开心': 'happy',
            '难过': 'sad',
            '成功': 'success',
            '失败': 'failure',
            '爱情': 'love',
            '工作': 'work'
        }
        
        for keyword, tag in keywords.items():
            if keyword in content:
                tags.append(tag)
        
        return list(set(tags))  # 去重
    
    def _update_indexes(self, memory: Memory):
        """
        更新索引系统
        
        索引就像图书馆的目录，帮助快速找到需要的记忆
        """
        # 更新类型索引
        if memory.type not in self.type_index:
            self.type_index[memory.type] = []
        self.type_index[memory.type].append(memory.id)
        
        # 更新情绪索引
        if memory.emotion not in self.emotion_index:
            self.emotion_index[memory.emotion] = []
        self.emotion_index[memory.emotion].append(memory.id)
        
        # 更新参与者索引
        for participant in memory.participants:
            if participant not in self.participant_index:
                self.participant_index[participant] = []
            self.participant_index[participant].append(memory.id)
    
    def get_memories_by_type(self, memory_type: str) -> List[Memory]:
        """
        按类型获取记忆
        
        比如获取所有"学术"类型的记忆
        """
        if memory_type not in self.type_index:
            return []
        
        memory_ids = self.type_index[memory_type]
        memories = []
        
        for memory in self.memories:
            if memory.id in memory_ids:
                memory.access()  # 记录访问
                memories.append(memory)
        
        return memories
    
    def get_memories_about_person(self, person_name: str) -> List[Memory]:
        """
        获取关于某个人的所有记忆
        
        比如获取所有关于"小明"的记忆
        """
        if person_name not in self.participant_index:
            return []
        
        memory_ids = self.participant_index[person_name]
        memories = []
        
        for memory in self.memories:
            if memory.id in memory_ids:
                memory.access()  # 记录访问
                memories.append(memory)
        
        return memories
    
    def search_memories(self, keyword: str, limit: int = 5) -> List[Memory]:
        """
        搜索记忆
        
        根据关键词在记忆内容中搜索
        """
        print(f"\n🔍 {self.character_name} 正在回忆关于 '{keyword}' 的事情...")
        
        matching_memories = []
        
        for memory in self.memories:
            # 在内容、标签中搜索关键词
            if (keyword.lower() in memory.content.lower() or 
                keyword.lower() in [tag.lower() for tag in memory.tags]):
                
                memory.access()  # 记录访问
                matching_memories.append(memory)
        
        # 按相关性排序（这里简化为按强度和重要性排序）
        matching_memories.sort(
            key=lambda m: m.strength * m.importance, 
            reverse=True
        )
        
        result = matching_memories[:limit]
        print(f"💭 找到 {len(result)} 条相关记忆")
        
        return result
    
    def get_recent_memories(self, limit: int = 10) -> List[Memory]:
        """
        获取最近的记忆
        """
        # 按创建时间排序
        recent = sorted(self.memories, key=lambda m: m.created_time, reverse=True)
        return recent[:limit]
    
    def get_important_memories(self, limit: int = 10) -> List[Memory]:
        """
        获取最重要的记忆
        """
        # 按重要性和强度排序
        important = sorted(
            self.memories, 
            key=lambda m: m.importance * m.strength, 
            reverse=True
        )
        return important[:limit]
    
    def update_memory_strength(self):
        """
        更新所有记忆的强度（模拟遗忘过程）
        
        这个函数应该定期调用，比如每天一次
        """
        print(f"\n🧠 {self.character_name} 的记忆正在自然衰减...")
        
        for memory in self.memories:
            memory.decay()
        
        # 移除强度太低的记忆（完全遗忘）
        self.memories = [m for m in self.memories if m.strength > 0.1]
        
        print(f"💭 当前记忆数量: {len(self.memories)}")
    
    def _cleanup_old_memories(self):
        """
        清理旧记忆（当记忆数量超过限制时）
        
        保留重要的和最近的记忆，删除不重要的旧记忆
        """
        print(f"\n🧹 记忆数量超限，正在清理旧记忆...")
        
        # 按综合分数排序（重要性 * 强度 * 时间因子）
        def memory_score(memory):
            time_factor = 1.0  # 可以根据时间调整
            return memory.importance * memory.strength * time_factor
        
        self.memories.sort(key=memory_score, reverse=True)
        
        # 保留前max_memories条记忆
        removed_count = len(self.memories) - self.max_memories
        self.memories = self.memories[:self.max_memories]
        
        # 重建索引
        self._rebuild_indexes()
        
        print(f"🗑️ 已清理 {removed_count} 条旧记忆")
    
    def _rebuild_indexes(self):
        """
        重建所有索引
        """
        self.type_index.clear()
        self.emotion_index.clear()
        self.participant_index.clear()
        
        for memory in self.memories:
            self._update_indexes(memory)
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """
        获取记忆统计信息
        """
        if not self.memories:
            return {"total": 0}
        
        # 按类型统计
        type_stats = {}
        for memory in self.memories:
            type_stats[memory.type] = type_stats.get(memory.type, 0) + 1
        
        # 按情绪统计
        emotion_stats = {}
        for memory in self.memories:
            emotion_stats[memory.emotion] = emotion_stats.get(memory.emotion, 0) + 1
        
        # 计算平均值
        avg_importance = sum(m.importance for m in self.memories) / len(self.memories)
        avg_strength = sum(m.strength for m in self.memories) / len(self.memories)
        
        return {
            "total": len(self.memories),
            "avg_importance": round(avg_importance, 2),
            "avg_strength": round(avg_strength, 2),
            "type_distribution": type_stats,
            "emotion_distribution": emotion_stats,
            "oldest_memory": min(self.memories, key=lambda m: m.created_time).created_time.strftime("%Y-%m-%d %H:%M"),
            "newest_memory": max(self.memories, key=lambda m: m.created_time).created_time.strftime("%Y-%m-%d %H:%M")
        }
    
    def save_to_file(self, filename: str):
        """
        将记忆保存到文件
        """
        data = {
            "character_name": self.character_name,
            "memories": [memory.to_dict() for memory in self.memories]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 记忆已保存到 {filename}")
    
    def __str__(self):
        """
        定义如何打印记忆系统
        """
        return f"{self.character_name}的记忆系统 (共{len(self.memories)}条记忆)"


# 第三步：演示如何使用记忆系统
def demo_memory_system():
    """
    演示记忆系统的使用方法
    """
    print("🎓 记忆系统使用教程")
    print("=" * 50)
    
    # 创建王浩然的记忆系统
    haoran_memory = MemorySystem("王浩然")
    
    print(f"\n1️⃣ 创建记忆系统: {haoran_memory}")
    
    # 添加一些记忆
    print(f"\n2️⃣ 添加记忆...")
    
    # 背景记忆
    haoran_memory.add_memory(
        content="我是计算机科学研究生，对技术充满热情",
        memory_type="background",
        emotion="determined",
        importance=9
    )
    
    # 学术记忆
    haoran_memory.add_memory(
        content="今天算法优化取得突破，效率提升了30%",
        memory_type="academic",
        emotion="proud",
        importance=8,
        location="实验室"
    )
    
    # 社交记忆
    haoran_memory.add_memory(
        content="在图书馆遇到了陈晓晓，她很有活力",
        memory_type="social",
        emotion="curious",
        importance=6,
        participants=["陈晓晓"],
        location="图书馆"
    )
    
    # 情感记忆
    haoran_memory.add_memory(
        content="和陈晓晓一起学习，发现我们很合得来",
        memory_type="emotional",
        emotion="happy",
        importance=7,
        participants=["陈晓晓"],
        location="咖啡厅"
    )
    
    # 显示统计信息
    print(f"\n3️⃣ 记忆统计:")
    stats = haoran_memory.get_memory_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # 搜索记忆
    print(f"\n4️⃣ 搜索记忆:")
    memories = haoran_memory.search_memories("陈晓晓", limit=3)
    for i, memory in enumerate(memories, 1):
        print(f"   {i}. {memory}")
    
    # 按类型获取记忆
    print(f"\n5️⃣ 获取学术记忆:")
    academic_memories = haoran_memory.get_memories_by_type("academic")
    for memory in academic_memories:
        print(f"   - {memory}")
    
    # 获取重要记忆
    print(f"\n6️⃣ 最重要的记忆:")
    important_memories = haoran_memory.get_important_memories(limit=3)
    for i, memory in enumerate(important_memories, 1):
        print(f"   {i}. {memory}")
    
    # 模拟时间流逝
    print(f"\n7️⃣ 模拟时间流逝（记忆衰减）:")
    haoran_memory.update_memory_strength()
    
    # 保存记忆
    print(f"\n8️⃣ 保存记忆到文件:")
    haoran_memory.save_to_file("haoran_memories.json")
    
    print(f"\n✨ 教程完成！")
    print(f"记忆系统现在包含 {len(haoran_memory.memories)} 条记忆")
    
    return haoran_memory


if __name__ == "__main__":
    # 运行演示
    memory_system = demo_memory_system()

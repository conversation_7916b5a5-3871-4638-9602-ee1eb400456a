/**
 * 前端应用主文件 - 斯坦福小镇成长故事系统
 */

class StoryApp {
    constructor() {
        this.socket = io();
        this.characters = {};
        this.interactions = [];
        this.storyProgress = null;
        this.autoMode = true;
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupSocketListeners();
        
        // 初始化完成后隐藏加载指示器
        setTimeout(() => {
            this.hideLoading();
        }, 2000);
    }
    
    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 基本元素
        this.currentDayEl = document.getElementById('current-day');
        this.totalInteractionsEl = document.getElementById('total-interactions');
        this.charactersGrid = document.getElementById('characters-grid');
        this.interactionsContainer = document.getElementById('interactions-container');
        this.relationshipsContainer = document.getElementById('relationships-container');
        
        // 控制元素
        this.participant1Select = document.getElementById('participant1');
        this.participant2Select = document.getElementById('participant2');
        this.locationSelect = document.getElementById('location');
        this.triggerBtn = document.getElementById('trigger-interaction');
        this.nextDayBtn = document.getElementById('next-day-btn');
        this.resetBtn = document.getElementById('reset-btn');
        this.autoModeCheckbox = document.getElementById('auto-mode');
        
        // 模态框
        this.interactionModal = document.getElementById('interaction-modal');
        this.characterModal = document.getElementById('character-modal');
        this.memoryModal = document.getElementById('memory-modal');
        
        // 加载和通知
        this.loadingEl = document.getElementById('loading');
        this.notificationsEl = document.getElementById('notifications');
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 触发交互按钮
        this.triggerBtn.addEventListener('click', () => {
            this.triggerInteraction();
        });
        
        // 推进到下一天
        this.nextDayBtn.addEventListener('click', () => {
            this.nextDay();
        });
        
        // 重置故事
        this.resetBtn.addEventListener('click', () => {
            if (confirm('确定要重置整个故事吗？这将清除所有进度。')) {
                this.resetStory();
            }
        });
        
        // 自动模式切换
        this.autoModeCheckbox.addEventListener('change', (e) => {
            this.autoMode = e.target.checked;
        });
        
        // 模态框关闭
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.target.closest('.modal').style.display = 'none';
            });
        });
        
        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }
    
    /**
     * 设置Socket.IO监听器
     */
    setupSocketListeners() {
        // 系统状态更新
        this.socket.on('system_status', (data) => {
            this.characters = data.characters;
            this.storyProgress = data.storyProgress;
            this.updateUI();
        });
        
        // 新交互
        this.socket.on('new_interaction', (interaction) => {
            this.interactions.unshift(interaction);
            this.addInteractionToUI(interaction);
            this.updateCharacters();
            this.showNotification(`新交互: ${interaction.participantNames.join(' & ')}`, 'info');
        });
        
        // 新的一天
        this.socket.on('new_day', (progress) => {
            this.storyProgress = progress;
            this.updateStoryProgress();
            this.showNotification(`故事推进到第 ${progress.day} 天`, 'success');
        });
        
        // 故事重置
        this.socket.on('story_reset', () => {
            this.interactions = [];
            this.updateUI();
            this.showNotification('故事已重置', 'info');
        });
        
        // 错误处理
        this.socket.on('error', (error) => {
            this.showNotification(`错误: ${error.message}`, 'error');
        });
    }
    
    /**
     * 更新整个UI
     */
    async updateUI() {
        await this.updateCharacters();
        await this.updateInteractions();
        await this.updateRelationships();
        this.updateStoryProgress();
        this.updateParticipantSelects();
    }
    
    /**
     * 更新角色显示
     */
    async updateCharacters() {
        try {
            const response = await fetch('/api/characters');
            const result = await response.json();
            
            if (result.success) {
                this.characters = result.data;
                this.renderCharacters();
            }
        } catch (error) {
            console.error('Failed to update characters:', error);
        }
    }
    
    /**
     * 渲染角色卡片
     */
    renderCharacters() {
        this.charactersGrid.innerHTML = '';
        
        Object.entries(this.characters).forEach(([characterId, character]) => {
            const card = this.createCharacterCard(characterId, character);
            this.charactersGrid.appendChild(card);
        });
    }
    
    /**
     * 创建角色卡片
     */
    createCharacterCard(characterId, character) {
        const card = document.createElement('div');
        card.className = 'character-card';
        card.onclick = () => this.showCharacterDetails(characterId, character);
        
        // 角色头像颜色
        const avatarColors = {
            xiayu: '#ff69b4',
            xiaoxiao: '#32cd32', 
            haoran: '#4169e1'
        };
        
        card.innerHTML = `
            <div class="character-header">
                <div class="character-avatar" style="background-color: ${avatarColors[characterId] || '#808080'}">
                    ${character.name.charAt(0)}
                </div>
                <div>
                    <div class="character-name">${character.name}</div>
                    <div class="character-occupation">${character.occupation}</div>
                </div>
            </div>
            <div class="character-status">
                <div class="status-item">
                    <div class="emotion-indicator emotion-${character.currentState.emotion}"></div>
                    <span>${this.getEmotionText(character.currentState.emotion)}</span>
                </div>
                <div class="status-item">
                    <span>⚡ ${character.currentState.energy}%</span>
                </div>
                <div class="status-item">
                    <span>📍 ${this.getLocationText(character.currentState.location)}</span>
                </div>
                <div class="status-item">
                    <span>🎯 ${character.currentState.goals.length} 个目标</span>
                </div>
            </div>
        `;
        
        return card;
    }
    
    /**
     * 更新交互历史
     */
    async updateInteractions() {
        try {
            const response = await fetch('/api/interactions?limit=20');
            const result = await response.json();
            
            if (result.success) {
                this.interactions = result.data;
                this.renderInteractions();
            }
        } catch (error) {
            console.error('Failed to update interactions:', error);
        }
    }
    
    /**
     * 渲染交互历史
     */
    renderInteractions() {
        if (this.interactions.length === 0) {
            this.interactionsContainer.innerHTML = '<div class="no-interactions">暂无交互记录</div>';
            return;
        }
        
        this.interactionsContainer.innerHTML = '';
        
        this.interactions.forEach(interaction => {
            const item = this.createInteractionItem(interaction);
            this.interactionsContainer.appendChild(item);
        });
    }
    
    /**
     * 创建交互项目
     */
    createInteractionItem(interaction) {
        const item = document.createElement('div');
        item.className = 'interaction-item';
        item.onclick = () => this.showInteractionDetails(interaction);
        
        const timeStr = new Date(interaction.timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const preview = interaction.dialogue.length > 0 
            ? interaction.dialogue[0].content.substring(0, 50) + '...'
            : '无对话内容';
        
        item.innerHTML = `
            <div class="interaction-header">
                <div class="interaction-participants">${interaction.participantNames.join(' & ')}</div>
                <div class="interaction-time">${timeStr}</div>
            </div>
            <div class="interaction-preview">${preview}</div>
            <div class="interaction-location">${this.getLocationText(interaction.location)}</div>
        `;
        
        return item;
    }
    
    /**
     * 添加新交互到UI
     */
    addInteractionToUI(interaction) {
        const item = this.createInteractionItem(interaction);
        this.interactionsContainer.insertBefore(item, this.interactionsContainer.firstChild);
        
        // 移除"暂无交互记录"提示
        const noInteractions = this.interactionsContainer.querySelector('.no-interactions');
        if (noInteractions) {
            noInteractions.remove();
        }
        
        // 限制显示数量
        const items = this.interactionsContainer.querySelectorAll('.interaction-item');
        if (items.length > 20) {
            items[items.length - 1].remove();
        }
    }
    
    /**
     * 更新关系网络
     */
    async updateRelationships() {
        try {
            const response = await fetch('/api/relationships');
            const result = await response.json();
            
            if (result.success) {
                this.renderRelationships(result.data);
            }
        } catch (error) {
            console.error('Failed to update relationships:', error);
        }
    }
    
    /**
     * 渲染关系网络
     */
    renderRelationships(relationships) {
        this.relationshipsContainer.innerHTML = '';
        
        Object.entries(relationships).forEach(([characterId, relations]) => {
            const character = this.characters[characterId];
            if (!character) return;
            
            const group = document.createElement('div');
            group.className = 'relationship-group';
            
            const relationsList = Object.entries(relations).map(([otherId, relation]) => {
                return `
                    <div class="relationship-item">
                        <span>${relation.name}</span>
                        <span class="relationship-level relationship-${relation.type}">
                            ${this.getRelationshipText(relation.type)} (${relation.level})
                        </span>
                    </div>
                `;
            }).join('');
            
            group.innerHTML = `
                <div class="relationship-character">${character.name}</div>
                <div class="relationship-list">${relationsList}</div>
            `;
            
            this.relationshipsContainer.appendChild(group);
        });
    }
    
    /**
     * 更新故事进度
     */
    updateStoryProgress() {
        if (this.storyProgress) {
            this.currentDayEl.textContent = `第 ${this.storyProgress.day} 天`;
            this.totalInteractionsEl.textContent = `交互次数: ${this.storyProgress.totalInteractions}`;
        }
    }
    
    /**
     * 更新参与者选择框
     */
    updateParticipantSelects() {
        const selects = [this.participant1Select, this.participant2Select];
        
        selects.forEach(select => {
            const currentValue = select.value;
            select.innerHTML = '<option value="">选择角色</option>';
            
            Object.entries(this.characters).forEach(([characterId, character]) => {
                const option = document.createElement('option');
                option.value = characterId;
                option.textContent = character.name;
                select.appendChild(option);
            });
            
            select.value = currentValue;
        });
    }
    
    /**
     * 触发交互
     */
    async triggerInteraction() {
        const participant1 = this.participant1Select.value;
        const participant2 = this.participant2Select.value;
        const location = this.locationSelect.value;
        
        if (!participant1 || !participant2) {
            this.showNotification('请选择两个角色', 'error');
            return;
        }
        
        if (participant1 === participant2) {
            this.showNotification('请选择不同的角色', 'error');
            return;
        }
        
        try {
            this.showLoading();
            
            const response = await fetch('/api/interactions/trigger', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    participants: [participant1, participant2],
                    context: location ? { location } : {}
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('交互已触发', 'success');
            } else {
                this.showNotification(`触发失败: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`网络错误: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 推进到下一天
     */
    async nextDay() {
        try {
            const response = await fetch('/api/story/next-day', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('故事已推进到下一天', 'success');
            } else {
                this.showNotification(`推进失败: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`网络错误: ${error.message}`, 'error');
        }
    }
    
    /**
     * 重置故事
     */
    async resetStory() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/story/reset', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('故事已重置', 'success');
                // 重新加载页面数据
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                this.showNotification(`重置失败: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showNotification(`网络错误: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 显示角色详情
     */
    async showCharacterDetails(characterId, character) {
        try {
            // 获取角色记忆
            const response = await fetch(`/api/characters/${characterId}/memories`);
            const result = await response.json();
            
            const memories = result.success ? result.data : [];
            
            const modalTitle = document.getElementById('character-modal-title');
            const modalContent = document.getElementById('character-details');
            
            modalTitle.textContent = `${character.name} - 详细信息`;
            
            modalContent.innerHTML = `
                <div class="character-detail-section">
                    <h4>基本信息</h4>
                    <p><strong>姓名:</strong> ${character.name}</p>
                    <p><strong>年龄:</strong> ${character.age}</p>
                    <p><strong>职业:</strong> ${character.occupation}</p>
                    <p><strong>性格:</strong> ${character.personality.description}</p>
                </div>
                
                <div class="character-detail-section">
                    <h4>当前状态</h4>
                    <p><strong>情绪:</strong> ${this.getEmotionText(character.currentState.emotion)}</p>
                    <p><strong>精力:</strong> ${character.currentState.energy}%</p>
                    <p><strong>位置:</strong> ${this.getLocationText(character.currentState.location)}</p>
                    <p><strong>目标:</strong> ${character.currentState.goals.join(', ') || '无'}</p>
                </div>
                
                <div class="character-detail-section">
                    <h4>最近记忆 (${memories.length})</h4>
                    ${memories.map(memory => `
                        <div class="memory-item" onclick="app.showMemoryDetails('${memory.id}', '${character.name}')">
                            <div class="memory-content">${memory.content.substring(0, 100)}...</div>
                            <div class="memory-meta">
                                <span class="memory-type">${memory.type}</span>
                                <span class="memory-emotion emotion-${memory.emotion}">${this.getEmotionText(memory.emotion)}</span>
                                <span class="memory-importance">重要性: ${memory.importance}/10</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            
            this.characterModal.style.display = 'block';
        } catch (error) {
            this.showNotification(`获取角色详情失败: ${error.message}`, 'error');
        }
    }
    
    /**
     * 显示交互详情
     */
    showInteractionDetails(interaction) {
        const modalContent = document.getElementById('interaction-details');
        
        const timeStr = new Date(interaction.timestamp).toLocaleString('zh-CN');
        
        modalContent.innerHTML = `
            <div class="interaction-detail-section">
                <h4>基本信息</h4>
                <p><strong>参与者:</strong> ${interaction.participantNames.join(', ')}</p>
                <p><strong>时间:</strong> ${timeStr}</p>
                <p><strong>地点:</strong> ${this.getLocationText(interaction.location)}</p>
                <p><strong>类型:</strong> ${this.getInteractionTypeText(interaction.type)}</p>
            </div>
            
            <div class="interaction-detail-section">
                <h4>对话内容</h4>
                ${interaction.dialogue.map(d => `
                    <div class="dialogue-item">
                        <strong>${d.speaker}:</strong> ${d.content}
                        <span class="dialogue-emotion">(${this.getEmotionText(d.emotion)})</span>
                    </div>
                `).join('')}
            </div>
            
            ${interaction.actions.length > 0 ? `
                <div class="interaction-detail-section">
                    <h4>行为动作</h4>
                    ${interaction.actions.map(a => `
                        <div class="action-item">${a.description}</div>
                    `).join('')}
                </div>
            ` : ''}
            
            ${interaction.outcomes.length > 0 ? `
                <div class="interaction-detail-section">
                    <h4>交互结果</h4>
                    ${interaction.outcomes.map(o => `
                        <div class="outcome-item">${o.description}</div>
                    `).join('')}
                </div>
            ` : ''}
        `;
        
        this.interactionModal.style.display = 'block';
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        this.notificationsEl.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
    
    /**
     * 显示加载指示器
     */
    showLoading() {
        this.loadingEl.style.display = 'flex';
    }
    
    /**
     * 隐藏加载指示器
     */
    hideLoading() {
        this.loadingEl.style.display = 'none';
    }
    
    /**
     * 获取情绪文本
     */
    getEmotionText(emotion) {
        const emotionTexts = {
            'happy': '开心',
            'love': '爱意',
            'excited': '兴奋',
            'nervous': '紧张',
            'sad': '伤心',
            'angry': '生气',
            'neutral': '平静',
            'curious': '好奇',
            'embarrassed': '尴尬',
            'content': '满足'
        };
        return emotionTexts[emotion] || emotion;
    }
    
    /**
     * 获取位置文本
     */
    getLocationText(location) {
        const locationTexts = {
            'library': '图书馆',
            'coffee_shop': '咖啡厅',
            'campus': '校园',
            'cafeteria': '食堂',
            'park': '公园',
            'art_gallery': '美术馆',
            'home': '家'
        };
        return locationTexts[location] || location;
    }
    
    /**
     * 获取关系文本
     */
    getRelationshipText(type) {
        const relationshipTexts = {
            'love': '恋人',
            'close_friend': '密友',
            'friend': '朋友',
            'acquaintance': '熟人',
            'stranger': '陌生人',
            'dislike': '不喜欢'
        };
        return relationshipTexts[type] || type;
    }
    
    /**
     * 获取交互类型文本
     */
    getInteractionTypeText(type) {
        const typeTexts = {
            'first_meeting': '初次见面',
            'casual_interaction': '日常交流',
            'romantic_moment': '浪漫时刻',
            'awkward_moment': '尴尬时刻',
            'conflict': '冲突'
        };
        return typeTexts[type] || type;
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new StoryApp();
});

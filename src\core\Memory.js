const { v4: uuidv4 } = require('uuid');
const _ = require('lodash');

/**
 * 记忆系统 - 管理角色的记忆存储、检索和更新
 */
class Memory {
    constructor(characterId) {
        this.characterId = characterId;
        this.memories = new Map(); // 存储所有记忆
        this.memoryIndex = new Map(); // 记忆索引，用于快速检索
        this.emotionalMemories = new Map(); // 按情感分类的记忆
        this.relationshipMemories = new Map(); // 按关系分类的记忆
        this.timeBasedMemories = []; // 按时间排序的记忆
        
        this.maxMemories = 1000; // 最大记忆数量
        this.decayRate = 0.01; // 记忆衰减率
    }
    
    /**
     * 添加新记忆
     */
    async addMemory(memoryData) {
        const memory = {
            id: uuidv4(),
            characterId: this.characterId,
            type: memoryData.type,
            content: memoryData.content,
            participants: memoryData.participants || [],
            emotion: memoryData.emotion || 'neutral',
            importance: memoryData.importance || 5,
            timestamp: memoryData.timestamp || new Date(),
            location: memoryData.location || 'unknown',
            tags: this.extractTags(memoryData.content),
            accessCount: 0, // 访问次数
            lastAccessed: new Date(),
            strength: 1.0 // 记忆强度，会随时间衰减
        };
        
        // 存储记忆
        this.memories.set(memory.id, memory);
        
        // 更新索引
        this.updateIndexes(memory);
        
        // 按时间排序插入
        this.insertTimeBasedMemory(memory);
        
        // 检查是否需要清理旧记忆
        if (this.memories.size > this.maxMemories) {
            await this.cleanupOldMemories();
        }
        
        return memory.id;
    }
    
    /**
     * 提取记忆标签
     */
    extractTags(content) {
        const tags = new Set();
        
        // 简单的关键词提取
        const keywords = [
            '见面', '聊天', '笑', '哭', '生气', '开心', '紧张', '兴奋',
            '约会', '表白', '拒绝', '接受', '拥抱', '亲吻', '争吵', '和解',
            '工作', '学习', '吃饭', '散步', '看电影', '购物', '运动',
            '朋友', '恋人', '家人', '同事', '陌生人'
        ];
        
        keywords.forEach(keyword => {
            if (content.includes(keyword)) {
                tags.add(keyword);
            }
        });
        
        return Array.from(tags);
    }
    
    /**
     * 更新各种索引
     */
    updateIndexes(memory) {
        // 按类型索引
        if (!this.memoryIndex.has(memory.type)) {
            this.memoryIndex.set(memory.type, []);
        }
        this.memoryIndex.get(memory.type).push(memory.id);
        
        // 按情感索引
        if (!this.emotionalMemories.has(memory.emotion)) {
            this.emotionalMemories.set(memory.emotion, []);
        }
        this.emotionalMemories.get(memory.emotion).push(memory.id);
        
        // 按参与者索引
        memory.participants.forEach(participant => {
            if (!this.relationshipMemories.has(participant)) {
                this.relationshipMemories.set(participant, []);
            }
            this.relationshipMemories.get(participant).push(memory.id);
        });
    }
    
    /**
     * 按时间插入记忆
     */
    insertTimeBasedMemory(memory) {
        // 找到正确的插入位置
        let insertIndex = this.timeBasedMemories.length;
        for (let i = this.timeBasedMemories.length - 1; i >= 0; i--) {
            if (this.timeBasedMemories[i].timestamp <= memory.timestamp) {
                insertIndex = i + 1;
                break;
            }
        }
        this.timeBasedMemories.splice(insertIndex, 0, memory);
    }
    
    /**
     * 获取相关记忆
     */
    async getRelevantMemories(query) {
        const {
            character,
            context,
            emotion,
            timeRange,
            limit = 10
        } = query;
        
        let candidateMemories = [];
        
        // 如果指定了角色，获取与该角色相关的记忆
        if (character) {
            const characterMemories = this.relationshipMemories.get(character) || [];
            candidateMemories = characterMemories.map(id => this.memories.get(id));
        } else {
            candidateMemories = Array.from(this.memories.values());
        }
        
        // 过滤条件
        candidateMemories = candidateMemories.filter(memory => {
            if (!memory) return false;
            
            // 情感过滤
            if (emotion && memory.emotion !== emotion) return false;
            
            // 时间范围过滤
            if (timeRange) {
                const memoryTime = memory.timestamp.getTime();
                if (timeRange.start && memoryTime < timeRange.start.getTime()) return false;
                if (timeRange.end && memoryTime > timeRange.end.getTime()) return false;
            }
            
            // 上下文相关性
            if (context && context !== 'general') {
                const contextRelevant = memory.tags.some(tag => 
                    context.toLowerCase().includes(tag.toLowerCase()) ||
                    tag.toLowerCase().includes(context.toLowerCase())
                );
                if (!contextRelevant && !memory.content.toLowerCase().includes(context.toLowerCase())) {
                    return false;
                }
            }
            
            return true;
        });
        
        // 计算相关性分数并排序
        candidateMemories = candidateMemories.map(memory => ({
            ...memory,
            relevanceScore: this.calculateRelevanceScore(memory, query)
        }));
        
        candidateMemories.sort((a, b) => b.relevanceScore - a.relevanceScore);
        
        // 更新访问信息
        candidateMemories.slice(0, limit).forEach(memory => {
            memory.accessCount++;
            memory.lastAccessed = new Date();
        });
        
        return candidateMemories.slice(0, limit);
    }
    
    /**
     * 计算记忆相关性分数
     */
    calculateRelevanceScore(memory, query) {
        let score = 0;
        
        // 基础重要性
        score += memory.importance * 10;
        
        // 记忆强度
        score += memory.strength * 20;
        
        // 访问频率
        score += Math.log(memory.accessCount + 1) * 5;
        
        // 时间衰减
        const daysSinceCreation = (new Date() - memory.timestamp) / (1000 * 60 * 60 * 24);
        score -= daysSinceCreation * 0.1;
        
        // 最近访问加分
        const daysSinceAccess = (new Date() - memory.lastAccessed) / (1000 * 60 * 60 * 24);
        if (daysSinceAccess < 1) score += 10;
        else if (daysSinceAccess < 7) score += 5;
        
        // 情感强度
        const emotionIntensity = this.getEmotionIntensity(memory.emotion);
        score += emotionIntensity * 5;
        
        // 特殊事件类型加分
        const specialTypes = {
            'first_meeting': 15,
            'love_at_first_sight': 20,
            'romantic_moment': 15,
            'conflict': 10,
            'breakthrough': 12
        };
        
        if (specialTypes[memory.type]) {
            score += specialTypes[memory.type];
        }
        
        return score;
    }
    
    /**
     * 获取情感强度
     */
    getEmotionIntensity(emotion) {
        const intensities = {
            'love': 10,
            'hate': 9,
            'excitement': 8,
            'anger': 8,
            'joy': 7,
            'sadness': 7,
            'fear': 6,
            'surprise': 6,
            'nervousness': 5,
            'contentment': 4,
            'neutral': 1
        };
        
        return intensities[emotion] || 3;
    }
    
    /**
     * 获取最近的记忆
     */
    getRecentMemories(limit = 5) {
        return this.timeBasedMemories
            .slice(-limit)
            .reverse()
            .map(memory => ({
                id: memory.id,
                type: memory.type,
                content: memory.content,
                emotion: memory.emotion,
                timestamp: memory.timestamp,
                importance: memory.importance
            }));
    }
    
    /**
     * 获取特定类型的记忆
     */
    getMemoriesByType(type, limit = 10) {
        const memoryIds = this.memoryIndex.get(type) || [];
        return memoryIds
            .slice(-limit)
            .map(id => this.memories.get(id))
            .filter(memory => memory)
            .reverse();
    }
    
    /**
     * 获取与特定角色相关的记忆
     */
    getMemoriesWithCharacter(characterName, limit = 10) {
        const memoryIds = this.relationshipMemories.get(characterName) || [];
        return memoryIds
            .slice(-limit)
            .map(id => this.memories.get(id))
            .filter(memory => memory)
            .sort((a, b) => b.timestamp - a.timestamp);
    }
    
    /**
     * 更新记忆强度（模拟遗忘）
     */
    async updateMemoryStrength() {
        for (const memory of this.memories.values()) {
            // 计算时间衰减
            const daysSinceCreation = (new Date() - memory.timestamp) / (1000 * 60 * 60 * 24);
            const daysSinceAccess = (new Date() - memory.lastAccessed) / (1000 * 60 * 60 * 24);
            
            // 基础衰减
            let decay = this.decayRate * daysSinceCreation;
            
            // 未访问的记忆衰减更快
            if (daysSinceAccess > 30) {
                decay *= 2;
            }
            
            // 重要记忆衰减较慢
            if (memory.importance >= 8) {
                decay *= 0.5;
            }
            
            // 高情感强度记忆衰减较慢
            const emotionIntensity = this.getEmotionIntensity(memory.emotion);
            if (emotionIntensity >= 7) {
                decay *= 0.7;
            }
            
            memory.strength = Math.max(0.1, memory.strength - decay);
        }
    }
    
    /**
     * 清理旧记忆
     */
    async cleanupOldMemories() {
        // 获取所有记忆并按强度排序
        const allMemories = Array.from(this.memories.values());
        allMemories.sort((a, b) => a.strength - b.strength);
        
        // 删除最弱的记忆
        const toDelete = allMemories.slice(0, Math.floor(this.maxMemories * 0.1));
        
        toDelete.forEach(memory => {
            this.deleteMemory(memory.id);
        });
    }
    
    /**
     * 删除记忆
     */
    deleteMemory(memoryId) {
        const memory = this.memories.get(memoryId);
        if (!memory) return;
        
        // 从主存储删除
        this.memories.delete(memoryId);
        
        // 从索引中删除
        const typeIndex = this.memoryIndex.get(memory.type);
        if (typeIndex) {
            const index = typeIndex.indexOf(memoryId);
            if (index > -1) typeIndex.splice(index, 1);
        }
        
        const emotionIndex = this.emotionalMemories.get(memory.emotion);
        if (emotionIndex) {
            const index = emotionIndex.indexOf(memoryId);
            if (index > -1) emotionIndex.splice(index, 1);
        }
        
        memory.participants.forEach(participant => {
            const relationIndex = this.relationshipMemories.get(participant);
            if (relationIndex) {
                const index = relationIndex.indexOf(memoryId);
                if (index > -1) relationIndex.splice(index, 1);
            }
        });
        
        // 从时间索引删除
        const timeIndex = this.timeBasedMemories.findIndex(m => m.id === memoryId);
        if (timeIndex > -1) {
            this.timeBasedMemories.splice(timeIndex, 1);
        }
    }
    
    /**
     * 获取记忆统计信息
     */
    getMemoryStats() {
        const stats = {
            totalMemories: this.memories.size,
            memoryTypes: {},
            emotionalDistribution: {},
            averageStrength: 0,
            oldestMemory: null,
            newestMemory: null
        };
        
        let totalStrength = 0;
        let oldestTime = new Date();
        let newestTime = new Date(0);
        
        for (const memory of this.memories.values()) {
            // 类型统计
            stats.memoryTypes[memory.type] = (stats.memoryTypes[memory.type] || 0) + 1;
            
            // 情感统计
            stats.emotionalDistribution[memory.emotion] = (stats.emotionalDistribution[memory.emotion] || 0) + 1;
            
            // 强度统计
            totalStrength += memory.strength;
            
            // 时间统计
            if (memory.timestamp < oldestTime) {
                oldestTime = memory.timestamp;
                stats.oldestMemory = memory;
            }
            if (memory.timestamp > newestTime) {
                newestTime = memory.timestamp;
                stats.newestMemory = memory;
            }
        }
        
        stats.averageStrength = this.memories.size > 0 ? totalStrength / this.memories.size : 0;
        
        return stats;
    }
}

module.exports = Memory;

const { v4: uuidv4 } = require('uuid');
const { specialScenarios, dailyLifeScenarios } = require('../characters/CharacterDefinitions');

/**
 * 交互引擎 - 管理角色间的交互逻辑和故事推进
 */
class InteractionEngine {
    constructor(characterFactory) {
        this.characterFactory = characterFactory;
        this.interactionHistory = [];
        this.currentScene = null;
        this.storyProgress = {
            day: 1,
            totalInteractions: 0,
            significantEvents: []
        };
        
        // 对话模板
        this.dialogueTemplates = this.initializeDialogueTemplates();
        
        // 场景权重
        this.sceneWeights = {
            daily_life: 0.6,
            special_moment: 0.3,
            conflict: 0.1
        };
    }
    
    /**
     * 初始化对话模板
     */
    initializeDialogueTemplates() {
        return {
            // 初次见面
            first_meeting: {
                xiayu: [
                    "你好...我是林小雨，很高兴认识你。",
                    "嗯...我们之前见过吗？你看起来很面熟。",
                    "不好意思，我可能有点紧张...初次见面。"
                ],
                xiaoxiao: [
                    "嗨！我是陈晓晓，很高兴认识你！",
                    "你好呀！我觉得我们一定会成为好朋友的。",
                    "哇，终于正式认识了！我之前就注意到你了。"
                ],
                haoran: [
                    "你好，我是王浩然，请多指教。",
                    "很高兴认识你，我经常在图书馆看到你。",
                    "嗯...你好，我是浩然，希望我们能成为朋友。"
                ]
            },
            
            // 日常聊天
            casual_chat: {
                xiayu: [
                    "最近在画一幅新作品，灵感来自于校园里的梧桐树...",
                    "你觉得艺术能表达出语言无法描述的情感吗？",
                    "有时候我觉得，安静地坐着就是一种幸福。"
                ],
                xiaoxiao: [
                    "今天心理学课上学到了很有趣的理论！",
                    "你知道吗？人的第一印象其实很重要呢。",
                    "我觉得了解一个人的内心世界是件很有意思的事。"
                ],
                haoran: [
                    "最近在研究一个新的算法，虽然复杂但很有挑战性。",
                    "技术发展得这么快，有时候觉得要不断学习才能跟上。",
                    "你有什么兴趣爱好吗？我觉得工作之外也需要放松。"
                ]
            },
            
            // 表达好感
            showing_interest: {
                xiayu: [
                    "和你聊天让我感到很舒服...很少有人能理解我的想法。",
                    "你的眼神很温柔，让我想起了我画过的一幅肖像。",
                    "我...我觉得我们很合得来，也许可以经常见面？"
                ],
                xiaoxiao: [
                    "你真的很特别！和你在一起我感到很开心。",
                    "我发现我们有很多共同点，这种感觉真的很棒！",
                    "你知道吗？我很喜欢和你聊天的感觉。"
                ],
                haoran: [
                    "和你交流让我觉得很愉快，你的想法总是很有趣。",
                    "我发现自己开始期待和你见面了...这种感觉很特别。",
                    "也许我们可以一起去看场电影？如果你愿意的话。"
                ]
            },
            
            // 浪漫时刻
            romantic_moment: {
                xiayu: [
                    "这个时刻...就像我一直想要画下来的美好瞬间。",
                    "我的心跳得好快...这种感觉是不是就是心动？",
                    "和你在一起，我感到前所未有的安全感。"
                ],
                xiaoxiao: [
                    "哇，我的心脏跳得好快！这就是恋爱的感觉吗？",
                    "这一刻真的很美好，我想永远记住这种感觉。",
                    "你让我相信了一见钟情这种事情！"
                ],
                haoran: [
                    "我从来没有过这种感觉...心跳加速，却又很平静。",
                    "和你在一起的时候，我觉得整个世界都变得美好了。",
                    "我想...我可能已经喜欢上你了。"
                ]
            },
            
            // 尴尬时刻
            awkward_moment: {
                xiayu: [
                    "啊...不好意思，我刚才说错话了吗？",
                    "我...我不是那个意思...怎么办，好尴尬...",
                    "对不起，我有时候表达不太清楚..."
                ],
                xiaoxiao: [
                    "哈哈...这个场面有点尴尬呢，我们换个话题吧？",
                    "哎呀，我是不是说了什么不合适的话？",
                    "没关系没关系，这种事情很正常的！"
                ],
                haoran: [
                    "额...我刚才的话可能有点不合适，抱歉。",
                    "这个...我们是不是误会了什么？",
                    "不好意思，我不太善于处理这种情况。"
                ]
            }
        };
    }
    
    /**
     * 开始新的交互
     */
    async startInteraction(participants, context = {}) {
        // 参与者准备
        const preparedParticipants = [];
        for (const participant of participants) {
            const character = typeof participant === 'string' 
                ? this.characterFactory.getCharacter(participant) 
                : participant;
            
            if (character) {
                const otherParticipants = participants.filter(p => 
                    (typeof p === 'string' ? p : p.name) !== (typeof participant === 'string' ? participant : participant.name)
                ).map(p => typeof p === 'string' ? this.characterFactory.getCharacter(p) : p);
                
                const preparationResult = await character.prepareForInteraction(
                    otherParticipants[0], // 简化处理，主要考虑两人交互
                    context
                );
                
                preparedParticipants.push({
                    character,
                    preparation: preparationResult
                });
            }
        }
        
        // 确定交互类型和场景
        const interactionType = this.determineInteractionType(preparedParticipants, context);
        const scene = this.generateScene(interactionType, preparedParticipants, context);
        
        // 执行交互
        const interactionResult = await this.executeInteraction(scene, preparedParticipants);
        
        // 处理交互结果
        await this.processInteractionResult(interactionResult, preparedParticipants);
        
        // 更新故事进度
        this.updateStoryProgress(interactionResult);
        
        return interactionResult;
    }
    
    /**
     * 确定交互类型
     */
    determineInteractionType(participants, context) {
        // 检查是否是初次见面
        const isFirstMeeting = this.checkFirstMeeting(participants);
        if (isFirstMeeting) {
            return 'first_meeting';
        }
        
        // 检查特殊情感状态
        const emotionalStates = participants.map(p => p.character.currentEmotion);
        
        // 检查是否有浪漫倾向
        const hasRomanticTension = this.checkRomanticTension(participants);
        if (hasRomanticTension && Math.random() < 0.3) {
            return 'romantic_moment';
        }
        
        // 检查是否有冲突倾向
        const hasConflictTension = this.checkConflictTension(participants);
        if (hasConflictTension && Math.random() < 0.2) {
            return 'conflict';
        }
        
        // 检查是否可能发生尴尬时刻
        if (Math.random() < 0.15) {
            return 'awkward_moment';
        }
        
        // 默认为日常交互
        return 'casual_interaction';
    }
    
    /**
     * 检查是否是初次见面
     */
    checkFirstMeeting(participants) {
        if (participants.length !== 2) return false;
        
        const [p1, p2] = participants;
        const relationship1 = p1.character.relationships.get(p2.character.id);
        const relationship2 = p2.character.relationships.get(p1.character.id);
        
        return (!relationship1 || relationship1.type === 'stranger') && 
               (!relationship2 || relationship2.type === 'stranger');
    }
    
    /**
     * 检查浪漫张力
     */
    checkRomanticTension(participants) {
        return participants.some(p => 
            ['love', 'attraction', 'excited'].includes(p.character.currentEmotion) ||
            p.character.traits.has('romantic')
        );
    }
    
    /**
     * 检查冲突张力
     */
    checkConflictTension(participants) {
        return participants.some(p => 
            ['angry', 'frustrated', 'jealous'].includes(p.character.currentEmotion)
        );
    }
    
    /**
     * 生成场景
     */
    generateScene(interactionType, participants, context) {
        const scene = {
            id: uuidv4(),
            type: interactionType,
            participants: participants.map(p => p.character),
            location: context.location || this.selectRandomLocation(),
            timestamp: new Date(),
            context: context,
            mood: this.calculateSceneMood(participants),
            specialTriggers: []
        };
        
        // 检查特殊触发条件
        scene.specialTriggers = this.checkSpecialTriggers(scene);
        
        return scene;
    }
    
    /**
     * 选择随机位置
     */
    selectRandomLocation() {
        const locations = ['library', 'coffee_shop', 'campus', 'cafeteria', 'park', 'art_gallery'];
        return locations[Math.floor(Math.random() * locations.length)];
    }
    
    /**
     * 计算场景氛围
     */
    calculateSceneMood(participants) {
        const emotions = participants.map(p => p.character.currentEmotion);
        const avgEnergy = participants.reduce((sum, p) => sum + p.character.energy, 0) / participants.length;
        
        if (emotions.includes('love') || emotions.includes('excited')) {
            return 'romantic';
        } else if (emotions.includes('angry') || emotions.includes('frustrated')) {
            return 'tense';
        } else if (avgEnergy > 70) {
            return 'energetic';
        } else {
            return 'calm';
        }
    }
    
    /**
     * 检查特殊触发条件
     */
    checkSpecialTriggers(scene) {
        const triggers = [];
        
        // 一见钟情检查
        if (scene.type === 'first_meeting') {
            const loveAtFirstSightScenarios = specialScenarios.love_at_first_sight.triggers;
            for (const scenario of loveAtFirstSightScenarios) {
                const participantNames = scene.participants.map(p => 
                    Object.keys(this.characterFactory.characters).find(key => 
                        this.characterFactory.characters.get(key).name === p.name
                    )
                );
                
                if (scenario.characters.every(char => participantNames.includes(char)) &&
                    (!scenario.location || scenario.location === scene.location) &&
                    Math.random() < scenario.probability) {
                    triggers.push('love_at_first_sight');
                }
            }
        }
        
        return triggers;
    }
    
    /**
     * 执行交互
     */
    async executeInteraction(scene, participants) {
        const interaction = {
            id: scene.id,
            type: scene.type,
            participants: participants.map(p => p.character.id),
            participantNames: participants.map(p => p.character.name),
            location: scene.location,
            timestamp: scene.timestamp,
            dialogue: [],
            actions: [],
            emotions: {},
            outcomes: [],
            specialEvents: scene.specialTriggers
        };
        
        // 生成对话
        interaction.dialogue = await this.generateDialogue(scene, participants);
        
        // 生成行为
        interaction.actions = this.generateActions(scene, participants);
        
        // 记录情感状态
        participants.forEach(p => {
            interaction.emotions[p.character.name] = {
                before: p.preparation.emotion,
                after: p.character.currentEmotion,
                intensity: p.character.emotionSystem.emotionIntensity
            };
        });
        
        // 确定交互结果
        interaction.outcomes = this.determineOutcomes(scene, participants, interaction);
        
        return interaction;
    }
    
    /**
     * 生成对话
     */
    async generateDialogue(scene, participants) {
        const dialogue = [];
        const templates = this.dialogueTemplates[scene.type] || this.dialogueTemplates.casual_chat;
        
        // 为每个参与者生成对话
        for (let i = 0; i < participants.length; i++) {
            const participant = participants[i];
            const characterId = Object.keys(this.characterFactory.characters).find(key => 
                this.characterFactory.characters.get(key).name === participant.character.name
            );
            
            const characterTemplates = templates[characterId] || [];
            if (characterTemplates.length > 0) {
                const template = characterTemplates[Math.floor(Math.random() * characterTemplates.length)];
                
                dialogue.push({
                    speaker: participant.character.name,
                    content: template,
                    emotion: participant.character.currentEmotion,
                    timestamp: new Date(scene.timestamp.getTime() + i * 1000)
                });
            }
        }
        
        // 根据特殊触发事件调整对话
        if (scene.specialTriggers.includes('love_at_first_sight')) {
            dialogue.push({
                speaker: 'narrator',
                content: '两人的目光相遇的那一刻，仿佛时间都停止了...',
                emotion: 'romantic',
                timestamp: new Date(scene.timestamp.getTime() + dialogue.length * 1000)
            });
        }
        
        return dialogue;
    }
    
    /**
     * 生成行为
     */
    generateActions(scene, participants) {
        const actions = [];
        
        participants.forEach(participant => {
            const tendency = participant.character.generateBehaviorTendency(
                scene.type, 
                participants.filter(p => p !== participant).map(p => p.character)
            );
            
            // 根据行为倾向生成行为
            if (tendency.approach > 0.7) {
                actions.push({
                    actor: participant.character.name,
                    action: 'move_closer',
                    description: `${participant.character.name}主动靠近了一些`
                });
            }
            
            if (tendency.emotional_expression > 0.6) {
                actions.push({
                    actor: participant.character.name,
                    action: 'express_emotion',
                    description: `${participant.character.name}表达了自己的${participant.character.currentEmotion}情感`
                });
            }
            
            if (tendency.humor > 0.7) {
                actions.push({
                    actor: participant.character.name,
                    action: 'make_joke',
                    description: `${participant.character.name}开了个小玩笑来缓解气氛`
                });
            }
        });
        
        return actions;
    }
    
    /**
     * 确定交互结果
     */
    determineOutcomes(scene, participants, interaction) {
        const outcomes = [];
        
        // 关系变化
        if (scene.specialTriggers.includes('love_at_first_sight')) {
            outcomes.push({
                type: 'relationship_change',
                description: '一见钟情的火花点燃了',
                effect: 'positive',
                intensity: 'high'
            });
        }
        
        // 情感变化
        participants.forEach(participant => {
            const emotionBefore = interaction.emotions[participant.character.name].before;
            const emotionAfter = interaction.emotions[participant.character.name].after;
            
            if (emotionBefore !== emotionAfter) {
                outcomes.push({
                    type: 'emotion_change',
                    character: participant.character.name,
                    from: emotionBefore,
                    to: emotionAfter,
                    description: `${participant.character.name}的情绪从${emotionBefore}变为${emotionAfter}`
                });
            }
        });
        
        // 故事进展
        if (scene.type === 'first_meeting') {
            outcomes.push({
                type: 'story_progress',
                description: '新的相遇开启了故事的新篇章',
                significance: 'medium'
            });
        }
        
        return outcomes;
    }
    
    /**
     * 处理交互结果
     */
    async processInteractionResult(interaction, participants) {
        // 为每个参与者处理交互结果
        for (const participant of participants) {
            await participant.character.processInteractionResult({
                id: interaction.id,
                type: interaction.type,
                content: this.summarizeInteraction(interaction),
                participants: interaction.participants,
                emotion: participant.character.currentEmotion,
                timestamp: interaction.timestamp,
                location: interaction.location,
                outcomes: interaction.outcomes
            });
        }
        
        // 记录到交互历史
        this.interactionHistory.push(interaction);
        
        // 保持历史记录在合理范围内
        if (this.interactionHistory.length > 1000) {
            this.interactionHistory.shift();
        }
    }
    
    /**
     * 总结交互内容
     */
    summarizeInteraction(interaction) {
        const dialogueSummary = interaction.dialogue
            .map(d => `${d.speaker}: ${d.content}`)
            .join(' ');
        
        const actionSummary = interaction.actions
            .map(a => a.description)
            .join(' ');
        
        return `${dialogueSummary} ${actionSummary}`.trim();
    }
    
    /**
     * 更新故事进度
     */
    updateStoryProgress(interaction) {
        this.storyProgress.totalInteractions++;
        
        // 记录重要事件
        if (interaction.specialEvents.length > 0) {
            this.storyProgress.significantEvents.push({
                day: this.storyProgress.day,
                interaction: interaction.id,
                events: interaction.specialEvents,
                participants: interaction.participantNames
            });
        }
    }
    
    /**
     * 获取交互历史
     */
    getInteractionHistory(limit = 10) {
        return this.interactionHistory.slice(-limit).reverse();
    }
    
    /**
     * 获取故事进度
     */
    getStoryProgress() {
        return {
            ...this.storyProgress,
            recentInteractions: this.getInteractionHistory(5)
        };
    }
    
    /**
     * 推进故事到新的一天
     */
    advanceToNextDay() {
        this.storyProgress.day++;
        
        // 更新所有角色状态
        this.characterFactory.updateCharacterStates();
    }
}

module.exports = InteractionEngine;

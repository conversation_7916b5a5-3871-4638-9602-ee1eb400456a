/**
 * 测试组件 - 从test.js中提取的组件
 */

// 模拟UUID生成
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 简化的Memory类
class SimpleMemory {
    constructor(characterId) {
        this.characterId = characterId;
        this.memories = [];
    }
    
    async addMemory(memoryData) {
        const memory = {
            id: generateUUID(),
            ...memoryData,
            timestamp: new Date(),
            strength: 1.0
        };
        this.memories.push(memory);
        return memory.id;
    }
    
    async getRelevantMemories(query) {
        return this.memories
            .filter(m => !query.character || m.participants?.includes(query.character))
            .slice(-query.limit || 5);
    }
    
    getRecentMemories(limit = 5) {
        return this.memories.slice(-limit);
    }
    
    async updateMemoryStrength() {
        // 简化实现
    }
    
    getMemoryStats() {
        return {
            totalMemories: this.memories.length,
            averageStrength: 0.8
        };
    }
}

// 简化的EmotionSystem类
class SimpleEmotionSystem {
    constructor() {
        this.currentEmotion = 'neutral';
        this.emotionIntensity = 0.5;
        this.emotionHistory = [];
    }
    
    updateEmotionalState(memories, relationship) {
        // 简化的情感更新逻辑
        if (memories && memories.length > 0) {
            const lastMemory = memories[memories.length - 1];
            this.currentEmotion = lastMemory.emotion || 'neutral';
        }
        
        // 根据关系调整情感
        if (relationship && relationship.level > 3) {
            const emotions = ['happy', 'excited', 'content'];
            this.currentEmotion = emotions[Math.floor(Math.random() * emotions.length)];
        }
    }
    
    processInteraction(interaction) {
        // 根据交互类型调整情感
        const emotionMap = {
            'casual_chat': 'happy',
            'study_together': 'content',
            'chance_encounter': 'surprised',
            'romantic_moment': 'love',
            'awkward_moment': 'embarrassed'
        };
        
        this.currentEmotion = emotionMap[interaction.type] || interaction.emotion || 'neutral';
        
        this.emotionHistory.push({
            emotion: this.currentEmotion,
            timestamp: new Date()
        });
        
        // 保持历史记录在合理范围内
        if (this.emotionHistory.length > 20) {
            this.emotionHistory.shift();
        }
    }
    
    getCurrentEmotion() {
        return this.currentEmotion;
    }
    
    getEmotionIntensity() {
        return this.emotionIntensity;
    }
    
    getEmotionStats() {
        return {
            currentEmotion: this.currentEmotion,
            intensity: this.emotionIntensity,
            recentEmotions: this.emotionHistory.slice(-5)
        };
    }
    
    reset() {
        this.currentEmotion = 'neutral';
        this.emotionIntensity = 0.5;
        this.emotionHistory = [];
    }
}

// 简化的Character类
class SimpleCharacter {
    constructor(name, gender, personality, background) {
        this.id = generateUUID();
        this.name = name;
        this.gender = gender;
        this.personality = personality;
        this.background = background;
        
        this.memory = new SimpleMemory(this.id);
        this.emotionSystem = new SimpleEmotionSystem();
        
        this.currentEmotion = 'neutral';
        this.energy = 100;
        this.mood = 'normal';
        this.location = 'home';
        this.relationships = new Map();
        this.traits = new Set(personality.traits || []);
        this.goals = ['寻找真爱', '个人成长'];
        this.experiences = [];
    }
    
    async prepareForInteraction(otherCharacter, context = {}) {
        const relevantMemories = await this.memory.getRelevantMemories({
            character: otherCharacter.name,
            limit: 3
        });
        
        this.emotionSystem.updateEmotionalState(relevantMemories, this.relationships.get(otherCharacter.id));
        this.currentEmotion = this.emotionSystem.getCurrentEmotion();
        
        return {
            memories: relevantMemories,
            emotion: this.currentEmotion,
            relationship: this.relationships.get(otherCharacter.id) || { level: 0, type: 'stranger' }
        };
    }
    
    async processInteractionResult(interaction) {
        await this.memory.addMemory({
            type: 'interaction',
            content: interaction.content,
            participants: interaction.participants,
            emotion: interaction.emotion,
            importance: 5
        });
        
        this.emotionSystem.processInteraction(interaction);
        this.updateRelationships(interaction);
        this.updateGrowth(interaction);
    }
    
    updateRelationships(interaction) {
        interaction.participants.forEach(participantId => {
            if (participantId !== this.id) {
                const currentRelation = this.relationships.get(participantId) || {
                    level: 0,
                    type: 'stranger',
                    history: []
                };
                
                // 根据交互类型调整关系
                const relationshipBonus = {
                    'casual_chat': 1,
                    'study_together': 2,
                    'chance_encounter': 1,
                    'romantic_moment': 3,
                    'supportive_moment': 2
                };
                
                currentRelation.level += relationshipBonus[interaction.type] || 1;
                
                // 更新关系类型
                if (currentRelation.level >= 8) currentRelation.type = 'love';
                else if (currentRelation.level >= 6) currentRelation.type = 'close_friend';
                else if (currentRelation.level >= 3) currentRelation.type = 'friend';
                else if (currentRelation.level >= 1) currentRelation.type = 'acquaintance';
                
                currentRelation.history.push({
                    interaction: interaction.type,
                    timestamp: new Date()
                });
                
                this.relationships.set(participantId, currentRelation);
            }
        });
    }
    
    updateGrowth(interaction) {
        // 添加经历
        this.experiences.push({
            type: interaction.type,
            description: interaction.content,
            timestamp: new Date()
        });
        
        // 可能获得新的性格特质
        if (Math.random() < 0.1) { // 10% 概率
            const newTraits = {
                'casual_chat': 'sociable',
                'study_together': 'collaborative',
                'romantic_moment': 'romantic',
                'supportive_moment': 'caring'
            };
            
            const newTrait = newTraits[interaction.type];
            if (newTrait && !this.traits.has(newTrait)) {
                this.traits.add(newTrait);
            }
        }
        
        // 保持经历记录在合理范围内
        if (this.experiences.length > 50) {
            this.experiences.shift();
        }
    }
    
    getCurrentState() {
        return {
            id: this.id,
            name: this.name,
            emotion: this.currentEmotion,
            mood: this.mood,
            energy: this.energy,
            location: this.location,
            relationships: Object.fromEntries(this.relationships),
            traits: Array.from(this.traits),
            goals: this.goals,
            recentMemories: this.memory.getRecentMemories(3)
        };
    }
    
    generateBehaviorTendency(situation, otherCharacters) {
        const tendency = {
            approach: 0.5,
            openness: 0.5,
            emotional_expression: 0.5,
            humor: 0.5,
            romantic: 0.5
        };
        
        // 根据性格调整
        this.personality.traits.forEach(trait => {
            switch(trait) {
                case 'outgoing':
                    tendency.approach += 0.2;
                    tendency.openness += 0.2;
                    break;
                case 'introverted':
                    tendency.approach -= 0.1;
                    tendency.emotional_expression -= 0.1;
                    break;
                case 'romantic':
                    tendency.romantic += 0.3;
                    break;
                case 'empathetic':
                    tendency.emotional_expression += 0.2;
                    break;
            }
        });
        
        // 根据当前情绪调整
        const emotionAdjustments = {
            'happy': { openness: 0.2, humor: 0.2 },
            'nervous': { approach: -0.2, emotional_expression: -0.1 },
            'excited': { approach: 0.3, emotional_expression: 0.2 },
            'love': { romantic: 0.4, approach: 0.2 }
        };
        
        const adjustment = emotionAdjustments[this.currentEmotion];
        if (adjustment) {
            Object.keys(adjustment).forEach(key => {
                tendency[key] = Math.max(0, Math.min(1, tendency[key] + adjustment[key]));
            });
        }
        
        return tendency;
    }
}

module.exports = {
    generateUUID,
    SimpleMemory,
    SimpleEmotionSystem,
    SimpleCharacter
};

/**
 * 王浩然实际记忆内容分析 - 检查运行时产生的真实记忆
 */

const { generateUUID, SimpleMemory, SimpleEmotionSystem, SimpleCharacter } = require('./test-components');

class HaoranActualMemoryAnalyzer {
    constructor() {
        this.haoran = null;
        this.memoryTimeline = [];
    }
    
    async recreateRuntimeHaoran() {
        console.log('🔍 王浩然实际记忆内容分析');
        console.log('='.repeat(60));
        
        // 重新创建王浩然，模拟刚才程序运行的状态
        this.haoran = new SimpleCharacter(
            '王浩然',
            'male',
            { traits: ['intelligent', 'reliable', 'gentle'] },
            '聪明可靠的计算机科学研究生，理性而温和'
        );
        
        console.log('📋 初始状态检查:');
        console.log(`记忆数量: ${this.haoran.memory.memories.length}`);
        console.log(`关系数量: ${this.haoran.relationships.size}`);
        console.log(`当前情绪: ${this.haoran.currentEmotion}`);
        
        // 添加初始记忆（程序启动时的状态）
        await this.addInitialMemory();
        
        // 模拟刚才观察到的交互序列
        await this.simulateObservedInteractions();
        
        console.log('\n✅ 运行时状态重现完成');
    }
    
    async addInitialMemory() {
        console.log('\n📝 添加初始记忆...');
        
        // 这是程序启动时自动添加的背景记忆
        await this.haoran.memory.addMemory({
            type: 'background',
            content: '我是计算机科学的研究生，对技术充满热情，也希望找到生活的平衡。',
            emotion: 'thoughtful',
            importance: 8
        });
        
        this.recordMemoryEvent('程序启动', '系统自动添加背景记忆', 'background');
        
        console.log('✅ 初始记忆添加完成');
        console.log(`当前记忆数: ${this.haoran.memory.memories.length}`);
    }
    
    async simulateObservedInteractions() {
        console.log('\n🎭 模拟观察到的交互序列...');
        
        // 根据刚才程序输出，王浩然参与的交互
        const haoranInteractions = [
            {
                sequence: 1,
                participants: ['xiaoxiao', 'haoran'],
                location: '校园',
                type: 'chance_encounter',
                description: '与陈晓晓在校园偶遇，开始了第一次正式交流'
            },
            {
                sequence: 2,
                participants: ['haoran', 'xiayu'],
                location: '食堂',
                type: 'casual_chat',
                description: '与林小雨在食堂相遇，进行了简短但温暖的对话'
            },
            {
                sequence: 3,
                participants: ['haoran', 'xiaoxiao'],
                location: '咖啡厅',
                type: 'study_together',
                description: '与陈晓晓在咖啡厅一起学习，发现彼此的共同点'
            },
            {
                sequence: 4,
                participants: ['haoran', 'xiaoxiao'],
                location: '图书馆',
                type: 'study_together',
                description: '再次与陈晓晓在图书馆学习，关系进一步加深'
            },
            {
                sequence: 5,
                participants: ['haoran', 'xiaoxiao'],
                location: '图书馆',
                type: 'study_together',
                description: '第三次与陈晓晓在图书馆相遇，已经很自然地坐在一起'
            },
            {
                sequence: 6,
                participants: ['haoran', 'xiaoxiao'],
                location: '食堂',
                type: 'casual_chat',
                description: '与陈晓晓在食堂共进午餐，聊起了各自的专业和兴趣'
            }
        ];
        
        for (const interaction of haoranInteractions) {
            await this.processInteraction(interaction);
        }
    }
    
    async processInteraction(interactionData) {
        const { sequence, participants, location, type, description } = interactionData;
        
        console.log(`\n📍 交互 ${sequence}: ${description}`);
        
        // 交互前 - 读取相关记忆
        const otherCharacterName = participants.find(p => p !== 'haoran');
        const relevantMemories = await this.haoran.memory.getRelevantMemories({
            character: otherCharacterName,
            limit: 3
        });
        
        console.log(`  🧠 读取了 ${relevantMemories.length} 条相关记忆`);
        
        // 生成交互内容
        const interactionContent = this.generateInteractionContent(otherCharacterName, location, type, sequence);
        
        // 确定情绪变化
        const emotion = this.determineEmotionForInteraction(type, sequence, otherCharacterName);
        
        // 处理交互结果 - 添加新记忆
        await this.haoran.processInteractionResult({
            id: generateUUID(),
            type: type,
            content: interactionContent,
            participants: [this.haoran.id, 'other_character_id'], // 简化处理
            emotion: emotion,
            location: location
        });
        
        console.log(`  💭 新增记忆: "${interactionContent.substring(0, 40)}..."`);
        console.log(`  😊 情绪变化: ${emotion}`);
        
        // 记录记忆事件
        this.recordMemoryEvent(
            `交互${sequence}`,
            `与${otherCharacterName}在${location}${this.getTypeText(type)}`,
            'interaction',
            emotion
        );
        
        // 显示关系变化
        const relationshipCount = this.haoran.relationships.size;
        console.log(`  👥 当前关系数: ${relationshipCount}`);
    }
    
    generateInteractionContent(otherCharacter, location, type, sequence) {
        const characterNames = {
            'xiaoxiao': '陈晓晓',
            'xiayu': '林小雨'
        };
        
        const otherName = characterNames[otherCharacter] || otherCharacter;
        
        const contentTemplates = {
            'chance_encounter': [
                `在${location}偶遇${otherName}，她主动和我打招呼，我们开始了自然的交流`,
                `意外在${location}遇到${otherName}，发现她很容易相处，聊得很愉快`,
                `在${location}碰到${otherName}，她的笑容很温暖，让我感到放松`
            ],
            'casual_chat': [
                `与${otherName}在${location}聊天，发现我们有很多共同话题`,
                `和${otherName}在${location}交流，她的观点很有趣，让我有新的思考`,
                `与${otherName}在${location}谈论学习和生活，感觉很舒适自然`
            ],
            'study_together': [
                `与${otherName}在${location}一起学习，她很专注，也很乐于分享想法`,
                `和${otherName}在${location}讨论学术问题，她的思维很敏锐`,
                `与${otherName}在${location}共同学习，发现我们的学习方式很互补`
            ]
        };
        
        const templates = contentTemplates[type] || [`与${otherName}在${location}互动`];
        const templateIndex = Math.min(sequence - 1, templates.length - 1);
        
        return templates[templateIndex];
    }
    
    determineEmotionForInteraction(type, sequence, otherCharacter) {
        // 根据交互类型、次数和对象确定情绪
        const baseEmotions = {
            'chance_encounter': 'curious',
            'casual_chat': 'happy',
            'study_together': 'content'
        };
        
        let emotion = baseEmotions[type] || 'neutral';
        
        // 随着与陈晓晓交互次数增加，情感逐渐加深
        if (otherCharacter === 'xiaoxiao') {
            if (sequence >= 3) emotion = 'interested';
            if (sequence >= 5) emotion = 'attracted';
            if (sequence >= 6) emotion = 'warm';
        }
        
        // 与林小雨的交互相对较少，保持温和
        if (otherCharacter === 'xiayu') {
            emotion = 'gentle';
        }
        
        return emotion;
    }
    
    getTypeText(type) {
        const typeTexts = {
            'chance_encounter': '偶遇',
            'casual_chat': '聊天',
            'study_together': '一起学习'
        };
        return typeTexts[type] || '互动';
    }
    
    recordMemoryEvent(trigger, description, memoryType, emotion = null) {
        this.memoryTimeline.push({
            timestamp: new Date(),
            trigger: trigger,
            description: description,
            memoryType: memoryType,
            emotion: emotion,
            totalMemories: this.haoran.memory.memories.length
        });
    }
    
    analyzeMemoryContent() {
        console.log('\n📚 王浩然实际记忆内容分析');
        console.log('='.repeat(50));
        
        const memories = this.haoran.memory.memories;
        
        console.log(`\n📊 记忆统计:`);
        console.log(`总记忆数: ${memories.length}`);
        console.log(`关系数: ${this.haoran.relationships.size}`);
        console.log(`当前情绪: ${this.haoran.currentEmotion}`);
        
        console.log('\n📝 详细记忆内容:');
        memories.forEach((memory, index) => {
            const timeStr = memory.timestamp.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            console.log(`\n${index + 1}. [${timeStr}] ${memory.type.toUpperCase()}`);
            console.log(`   内容: "${memory.content}"`);
            console.log(`   情绪: ${memory.emotion} | 重要性: ${memory.importance || 5}`);
            if (memory.participants && memory.participants.length > 0) {
                console.log(`   参与者: ${memory.participants.length}人`);
            }
            if (memory.location) {
                console.log(`   地点: ${memory.location}`);
            }
        });
    }
    
    analyzeMemoryFormationReasons() {
        console.log('\n🔍 记忆形成原因分析');
        console.log('='.repeat(50));
        
        console.log('\n📋 记忆时间线:');
        this.memoryTimeline.forEach((event, index) => {
            const timeStr = event.timestamp.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            console.log(`${index + 1}. [${timeStr}] ${event.trigger}`);
            console.log(`   └── ${event.description}`);
            console.log(`   └── 记忆类型: ${event.memoryType}`);
            if (event.emotion) {
                console.log(`   └── 产生情绪: ${event.emotion}`);
            }
            console.log(`   └── 累计记忆: ${event.totalMemories}条`);
        });
        
        console.log('\n💡 记忆形成机制:');
        console.log('1. 系统初始化 → 自动添加背景记忆');
        console.log('2. 角色交互 → 每次交互后自动生成新记忆');
        console.log('3. 情绪变化 → 记忆中包含当时的情绪状态');
        console.log('4. 关系发展 → 重复交互加深记忆的情感色彩');
        console.log('5. 地点关联 → 记忆与具体场所建立联系');
    }
    
    analyzeRelationshipDevelopment() {
        console.log('\n💕 关系发展分析');
        console.log('='.repeat(50));
        
        console.log('\n👥 当前关系状态:');
        if (this.haoran.relationships.size === 0) {
            console.log('暂无建立的关系记录');
        } else {
            for (const [otherId, relationship] of this.haoran.relationships) {
                console.log(`关系ID: ${otherId}`);
                console.log(`  等级: ${relationship.level}`);
                console.log(`  类型: ${relationship.type}`);
                console.log(`  历史: ${relationship.history?.length || 0}条记录`);
            }
        }
        
        // 分析社交记忆
        const socialMemories = this.haoran.memory.memories.filter(m => 
            m.type === 'interaction' && m.participants && m.participants.length > 0
        );
        
        console.log(`\n🤝 社交记忆分析:`);
        console.log(`社交记忆数: ${socialMemories.length}/${this.haoran.memory.memories.length}`);
        
        if (socialMemories.length > 0) {
            console.log('\n📈 社交记忆发展轨迹:');
            socialMemories.forEach((memory, index) => {
                console.log(`${index + 1}. ${memory.emotion} - "${memory.content.substring(0, 30)}..."`);
            });
            
            // 情感发展轨迹
            const emotionTrajectory = socialMemories.map(m => m.emotion);
            console.log(`\n💭 情感发展: ${emotionTrajectory.join(' → ')}`);
        }
    }
    
    explainMemoryAbsence() {
        console.log('\n❓ 为什么记忆内容相对较少？');
        console.log('='.repeat(50));
        
        console.log('\n🔍 原因分析:');
        console.log('1. 程序运行时间短 - 只运行了几分钟，交互次数有限');
        console.log('2. 自动交互频率 - 每30秒一次，总交互次数不多');
        console.log('3. 角色选择随机 - 王浩然不是每次都被选中参与交互');
        console.log('4. 简化实现 - 测试版本的记忆系统相对简化');
        console.log('5. 初始状态 - 角色刚刚"诞生"，还没有丰富的人生经历');
        
        console.log('\n📈 记忆增长预期:');
        console.log('• 随着程序运行时间增长，记忆会不断积累');
        console.log('• 不同类型的交互会产生不同类型的记忆');
        console.log('• 重要事件（如一见钟情）会产生高重要性记忆');
        console.log('• 关系发展会影响记忆的情感色彩');
        console.log('• 长期运行会形成丰富的个人历史');
        
        console.log('\n🚀 如果继续运行，王浩然会积累:');
        console.log('• 更多与陈晓晓和林小雨的互动记忆');
        console.log('• 不同场景下的情感体验记忆');
        console.log('• 学术和技术相关的成长记忆');
        console.log('• 日常生活的点滴记忆');
        console.log('• 重要时刻的深刻记忆（如表白、冲突等）');
    }
    
    async run() {
        await this.recreateRuntimeHaoran();
        this.analyzeMemoryContent();
        this.analyzeMemoryFormationReasons();
        this.analyzeRelationshipDevelopment();
        this.explainMemoryAbsence();
        
        console.log('\n✨ 总结');
        console.log('='.repeat(60));
        console.log('王浩然当前的记忆内容反映了一个刚刚开始社交生活的研究生：');
        console.log('• 📚 有明确的学术背景和个人定位');
        console.log('• 🤝 开始与同龄人建立社交联系');
        console.log('• 💭 情感状态随着交互逐渐丰富');
        console.log('• 🌱 处于关系发展的早期阶段');
        console.log('• 🔮 具有巨大的成长和发展潜力');
        console.log('\n这正是一个真实的记忆系统应有的状态 - 从简单开始，逐渐丰富！');
    }
}

// 运行分析
if (require.main === module) {
    const analyzer = new HaoranActualMemoryAnalyzer();
    analyzer.run().catch(console.error);
}

module.exports = HaoranActualMemoryAnalyzer;

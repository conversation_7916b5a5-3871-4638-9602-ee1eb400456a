/**
 * 记忆分析脚本 - 分析三位角色的记忆差异
 */

const { generateUUID, SimpleMemory, SimpleEmotionSystem, SimpleCharacter } = require('./test-components');

class MemoryAnalyzer {
    constructor() {
        this.characters = {};
        this.analysisResults = {};
    }
    
    async initializeCharacters() {
        // 创建三个角色
        this.characters.xiayu = new SimpleCharacter(
            '林小雨',
            'female',
            { traits: ['gentle', 'introverted', 'artistic', 'sensitive', 'thoughtful'] },
            '温柔内向的艺术系学生，从小就喜欢画画。性格温和，不太善于表达自己的感情，但内心世界很丰富。'
        );
        
        this.characters.xiaoxiao = new SimpleCharacter(
            '陈晓晓',
            'female',
            { traits: ['outgoing', 'energetic', 'empathetic', 'curious', 'optimistic'] },
            '活泼开朗的心理学系学生，善于观察和理解他人，总是充满正能量，是朋友圈里的开心果。'
        );
        
        this.characters.haoran = new SimpleCharacter(
            '王浩然',
            'male',
            { traits: ['intelligent', 'reliable', 'gentle', 'ambitious', 'thoughtful'] },
            '聪明可靠的计算机科学研究生，外表斯文内心坚定，既有理工科的逻辑思维，也有人文关怀。'
        );
        
        // 添加角色特定的初始记忆
        await this.addCharacterSpecificMemories();
        
        console.log('✅ 角色初始化完成');
    }
    
    async addCharacterSpecificMemories() {
        // 林小雨的记忆 - 艺术和内向特质相关
        await this.characters.xiayu.memory.addMemory({
            type: 'background',
            content: '我从小就喜欢画画，用色彩和线条表达内心的情感。虽然不善言辞，但画笔是我最好的朋友。',
            importance: 9,
            emotion: 'nostalgic'
        });
        
        await this.characters.xiayu.memory.addMemory({
            type: 'artistic',
            content: '今天完成了一幅夕阳下的校园画作，那种温暖的光线让我想起了家乡的黄昏。',
            importance: 7,
            emotion: 'peaceful'
        });
        
        await this.characters.xiayu.memory.addMemory({
            type: 'social_anxiety',
            content: '在课堂上被老师点名回答问题时，我紧张得说不出话来，脸红得像苹果一样。',
            importance: 6,
            emotion: 'embarrassed'
        });
        
        await this.characters.xiayu.memory.addMemory({
            type: 'dream',
            content: '我梦想有一天能举办个人画展，让更多人看到我眼中的世界，也希望能遇到真正理解我的人。',
            importance: 8,
            emotion: 'hopeful'
        });
        
        // 陈晓晓的记忆 - 心理学和社交特质相关
        await this.characters.xiaoxiao.memory.addMemory({
            type: 'background',
            content: '我选择心理学是因为对人的内心世界充满好奇，我相信每个人都有自己的故事和价值。',
            importance: 9,
            emotion: 'passionate'
        });
        
        await this.characters.xiaoxiao.memory.addMemory({
            type: 'academic',
            content: '今天学习了依恋理论，突然理解了为什么有些人在感情中会表现得不安全，这让我更想帮助他们。',
            importance: 8,
            emotion: 'enlightened'
        });
        
        await this.characters.xiaoxiao.memory.addMemory({
            type: 'social',
            content: '在社团活动中成功调解了两个朋友的矛盾，看到他们重新和好，我感到特别有成就感。',
            importance: 7,
            emotion: 'accomplished'
        });
        
        await this.characters.xiaoxiao.memory.addMemory({
            type: 'observation',
            content: '注意到图书馆里有个画画的女孩总是独自一人，她的眼神很温柔，我想和她成为朋友。',
            participants: ['林小雨'],
            importance: 5,
            emotion: 'curious'
        });
        
        // 王浩然的记忆 - 技术和理性特质相关
        await this.characters.haoran.memory.addMemory({
            type: 'background',
            content: '编程对我来说不只是工作，而是一种创造的艺术。通过代码，我可以构建出改变世界的东西。',
            importance: 9,
            emotion: 'determined'
        });
        
        await this.characters.haoran.memory.addMemory({
            type: 'academic',
            content: '今天的算法优化取得了突破，运行效率提升了30%，导师夸奖了我的创新思维。',
            importance: 8,
            emotion: 'proud'
        });
        
        await this.characters.haoran.memory.addMemory({
            type: 'reflection',
            content: '虽然在技术上很有成就感，但有时觉得生活缺少一些温暖，希望能找到志同道合的伴侣。',
            importance: 7,
            emotion: 'longing'
        });
        
        await this.characters.haoran.memory.addMemory({
            type: 'observation',
            content: '图书馆里有个学心理学的女生很有活力，她的笑容很感染人，让我想起了阳光。',
            participants: ['陈晓晓'],
            importance: 4,
            emotion: 'attracted'
        });
    }
    
    async simulateInteractions() {
        console.log('\n🎭 模拟角色交互...');
        
        // 模拟几次不同类型的交互
        const interactions = [
            {
                participants: ['xiayu', 'xiaoxiao'],
                type: 'first_meeting',
                location: '咖啡厅',
                content: '小雨和晓晓在咖啡厅初次正式交谈，晓晓主动搭话询问小雨的画作'
            },
            {
                participants: ['xiaoxiao', 'haoran'],
                type: 'study_together',
                location: '图书馆',
                content: '晓晓和浩然在图书馆偶遇，发现彼此都在为期末考试准备'
            },
            {
                participants: ['xiayu', 'haoran'],
                type: 'chance_encounter',
                location: '校园',
                content: '小雨在校园写生时，浩然路过被她的画作吸引，两人开始交流'
            },
            {
                participants: ['xiayu', 'xiaoxiao', 'haoran'],
                type: 'group_activity',
                location: '学生活动中心',
                content: '三人在学生活动中心参加社团活动，开始建立更深的友谊'
            }
        ];
        
        for (const interaction of interactions) {
            await this.processInteraction(interaction);
        }
    }
    
    async processInteraction(interactionData) {
        const { participants, type, location, content } = interactionData;
        
        console.log(`\n📍 ${location} - ${type}`);
        console.log(`参与者: ${participants.map(id => this.characters[id].name).join(', ')}`);
        
        // 为每个参与者处理交互
        for (const participantId of participants) {
            const character = this.characters[participantId];
            const otherParticipants = participants.filter(id => id !== participantId);
            
            // 交互前准备 - 读取相关记忆
            const otherNames = otherParticipants.map(id => this.characters[id].name);
            const relevantMemories = await character.memory.getRelevantMemories({
                character: otherNames[0], // 简化处理
                limit: 3
            });
            
            console.log(`${character.name}读取了${relevantMemories.length}条相关记忆`);
            
            // 处理交互结果 - 添加新记忆
            await character.processInteractionResult({
                id: generateUUID(),
                type: type,
                content: content,
                participants: participants.map(id => this.characters[id].id),
                emotion: this.getEmotionForInteraction(type, character.personality.traits),
                location: location
            });
        }
    }
    
    getEmotionForInteraction(type, traits) {
        const emotionMap = {
            'first_meeting': traits.includes('outgoing') ? 'excited' : 'nervous',
            'study_together': 'focused',
            'chance_encounter': 'surprised',
            'group_activity': 'happy'
        };
        return emotionMap[type] || 'neutral';
    }
    
    analyzeMemoryDifferences() {
        console.log('\n📊 记忆差异分析');
        console.log('='.repeat(50));
        
        Object.keys(this.characters).forEach(characterId => {
            const character = this.characters[characterId];
            const memories = character.memory.memories;
            
            console.log(`\n👤 ${character.name} (${character.gender})`);
            console.log(`总记忆数: ${memories.length}`);
            
            // 按类型分析
            const typeStats = {};
            const emotionStats = {};
            const importanceStats = [];
            
            memories.forEach(memory => {
                // 类型统计
                typeStats[memory.type] = (typeStats[memory.type] || 0) + 1;
                
                // 情感统计
                emotionStats[memory.emotion] = (emotionStats[memory.emotion] || 0) + 1;
                
                // 重要性统计
                importanceStats.push(memory.importance);
            });
            
            console.log('\n📝 记忆类型分布:');
            Object.entries(typeStats).forEach(([type, count]) => {
                console.log(`  - ${type}: ${count}条`);
            });
            
            console.log('\n💭 情感分布:');
            Object.entries(emotionStats).forEach(([emotion, count]) => {
                console.log(`  - ${emotion}: ${count}条`);
            });
            
            const avgImportance = importanceStats.reduce((sum, imp) => sum + imp, 0) / importanceStats.length;
            console.log(`\n⭐ 平均重要性: ${avgImportance.toFixed(2)}`);
            
            // 显示最重要的记忆
            const topMemories = memories
                .sort((a, b) => b.importance - a.importance)
                .slice(0, 3);
            
            console.log('\n🔝 最重要的记忆:');
            topMemories.forEach((memory, index) => {
                console.log(`  ${index + 1}. [${memory.type}] ${memory.content.substring(0, 50)}... (重要性: ${memory.importance})`);
            });
            
            // 关系记忆分析
            const relationshipMemories = memories.filter(m => m.participants && m.participants.length > 0);
            console.log(`\n👥 关系相关记忆: ${relationshipMemories.length}条`);
            
            const relationshipStats = {};
            relationshipMemories.forEach(memory => {
                memory.participants.forEach(participant => {
                    relationshipStats[participant] = (relationshipStats[participant] || 0) + 1;
                });
            });
            
            Object.entries(relationshipStats).forEach(([participant, count]) => {
                console.log(`  - 与${participant}: ${count}条记忆`);
            });
        });
    }
    
    compareCharacterMemoryPatterns() {
        console.log('\n🔍 角色记忆模式对比');
        console.log('='.repeat(50));
        
        const patterns = {};
        
        Object.keys(this.characters).forEach(characterId => {
            const character = this.characters[characterId];
            const memories = character.memory.memories;
            
            patterns[characterId] = {
                name: character.name,
                traits: Array.from(character.traits),
                memoryCount: memories.length,
                avgImportance: memories.reduce((sum, m) => sum + m.importance, 0) / memories.length,
                emotionalRange: [...new Set(memories.map(m => m.emotion))],
                memoryTypes: [...new Set(memories.map(m => m.type))],
                socialMemories: memories.filter(m => m.participants && m.participants.length > 0).length,
                personalMemories: memories.filter(m => !m.participants || m.participants.length === 0).length
            };
        });
        
        console.log('\n📈 记忆特征对比:');
        console.log('角色\t\t记忆数\t平均重要性\t社交记忆\t个人记忆\t情感种类');
        console.log('-'.repeat(80));
        
        Object.values(patterns).forEach(pattern => {
            console.log(`${pattern.name}\t\t${pattern.memoryCount}\t${pattern.avgImportance.toFixed(1)}\t\t${pattern.socialMemories}\t${pattern.personalMemories}\t${pattern.emotionalRange.length}`);
        });
        
        console.log('\n🎭 性格特质对记忆的影响:');
        
        // 林小雨 - 内向艺术型
        console.log('\n🎨 林小雨 (内向艺术型):');
        console.log('- 记忆特点: 更多个人反思和艺术创作相关记忆');
        console.log('- 情感倾向: 偏向内省和美学情感 (peaceful, nostalgic, hopeful)');
        console.log('- 社交记忆: 较少但质量高，更关注深层情感连接');
        
        // 陈晓晓 - 外向社交型
        console.log('\n🌟 陈晓晓 (外向社交型):');
        console.log('- 记忆特点: 更多社交互动和他人观察记忆');
        console.log('- 情感倾向: 积极向上的情感 (passionate, accomplished, curious)');
        console.log('- 社交记忆: 数量多且涉及面广，善于记住他人细节');
        
        // 王浩然 - 理性思考型
        console.log('\n💻 王浩然 (理性思考型):');
        console.log('- 记忆特点: 技术成就和逻辑思考相关记忆较多');
        console.log('- 情感倾向: 理性中带有情感需求 (determined, proud, longing)');
        console.log('- 社交记忆: 观察细致但表达含蓄，重视深度交流');
        
        return patterns;
    }
    
    async run() {
        console.log('🧠 记忆系统差异分析');
        console.log('='.repeat(50));
        
        await this.initializeCharacters();
        await this.simulateInteractions();
        this.analyzeMemoryDifferences();
        const patterns = this.compareCharacterMemoryPatterns();
        
        console.log('\n✨ 总结');
        console.log('='.repeat(50));
        console.log('三位角色的记忆系统体现了不同的个性特征:');
        console.log('1. 记忆内容反映性格: 艺术型vs社交型vs理性型');
        console.log('2. 情感表达方式不同: 内敛vs外向vs理性');
        console.log('3. 社交记忆模式差异: 深度vs广度vs观察');
        console.log('4. 记忆重要性评判标准因人而异');
        console.log('\n这种差异化的记忆系统让每个角色都有独特的认知和行为模式！');
        
        return patterns;
    }
}

// 运行分析
if (require.main === module) {
    const analyzer = new MemoryAnalyzer();
    analyzer.run().catch(console.error);
}

module.exports = MemoryAnalyzer;

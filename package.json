{"name": "gameon-story-system", "version": "1.0.0", "description": "A growth story system inspired by Stanford Town with three characters and memory modules", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "build": "webpack --mode production", "serve": "http-server dist -p 3000"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "html-webpack-plugin": "^5.5.3", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "http-server": "^14.1.1"}, "keywords": ["story", "ai", "characters", "memory", "growth", "simulation"], "author": "GameON Team", "license": "MIT"}
const { v4: uuidv4 } = require('uuid');
const Memory = require('./Memory');
const EmotionSystem = require('./EmotionSystem');

/**
 * 角色类 - 代表故事中的一个角色
 */
class Character {
    constructor(name, gender, personality, background) {
        this.id = uuidv4();
        this.name = name;
        this.gender = gender;
        this.personality = personality; // 性格特征
        this.background = background; // 背景故事
        
        // 记忆系统
        this.memory = new Memory(this.id);
        
        // 情感系统
        this.emotionSystem = new EmotionSystem();
        
        // 当前状态
        this.currentEmotion = 'neutral'; // 当前情绪
        this.energy = 100; // 精力值
        this.mood = 'normal'; // 心情
        this.location = 'home'; // 当前位置
        
        // 关系网络
        this.relationships = new Map(); // 与其他角色的关系
        
        // 成长属性
        this.experiences = []; // 经历过的事件
        this.traits = new Set(personality.traits || []); // 性格特质
        this.goals = []; // 当前目标
        
        // 时间相关
        this.lastInteraction = null;
        this.dailyRoutine = this.generateDailyRoutine();
        
        this.initializeCharacter();
    }
    
    /**
     * 初始化角色
     */
    initializeCharacter() {
        // 添加初始记忆
        this.memory.addMemory({
            type: 'background',
            content: this.background,
            importance: 10,
            emotion: 'neutral'
        });
        
        // 设置初始目标
        this.setInitialGoals();
    }
    
    /**
     * 设置初始目标
     */
    setInitialGoals() {
        const commonGoals = [
            '寻找真爱',
            '建立深厚的友谊',
            '实现个人成长',
            '享受日常生活'
        ];
        
        this.goals = commonGoals.slice(0, 2); // 随机选择2个初始目标
    }
    
    /**
     * 生成日常作息
     */
    generateDailyRoutine() {
        return {
            morning: ['起床', '洗漱', '吃早餐'],
            afternoon: ['工作/学习', '午餐', '休息'],
            evening: ['晚餐', '娱乐活动', '社交'],
            night: ['准备睡觉', '反思一天']
        };
    }
    
    /**
     * 在交互前读取相关记忆
     */
    async prepareForInteraction(otherCharacter, context = {}) {
        // 读取与该角色相关的记忆
        const relevantMemories = await this.memory.getRelevantMemories({
            character: otherCharacter.name,
            context: context.situation || 'general',
            limit: 5
        });
        
        // 更新情感状态
        this.emotionSystem.updateEmotionalState(relevantMemories, this.relationships.get(otherCharacter.id));
        
        // 设置当前情绪
        this.currentEmotion = this.emotionSystem.getCurrentEmotion();
        
        return {
            memories: relevantMemories,
            emotion: this.currentEmotion,
            relationship: this.relationships.get(otherCharacter.id) || { level: 0, type: 'stranger' }
        };
    }
    
    /**
     * 交互后整合新记忆
     */
    async processInteractionResult(interaction) {
        // 创建新记忆
        const newMemory = {
            type: 'interaction',
            content: interaction.content,
            participants: interaction.participants,
            emotion: interaction.emotion,
            importance: this.calculateImportance(interaction),
            timestamp: new Date(),
            location: this.location
        };
        
        // 添加到记忆系统
        await this.memory.addMemory(newMemory);
        
        // 更新关系
        this.updateRelationships(interaction);
        
        // 更新个人成长
        this.updateGrowth(interaction);
        
        // 情感系统处理
        this.emotionSystem.processInteraction(interaction);
    }
    
    /**
     * 计算记忆重要性
     */
    calculateImportance(interaction) {
        let importance = 5; // 基础重要性
        
        // 情感强度影响重要性
        const emotionIntensity = this.emotionSystem.getEmotionIntensity(interaction.emotion);
        importance += emotionIntensity * 2;
        
        // 特殊事件增加重要性
        if (interaction.type === 'first_meeting') importance += 3;
        if (interaction.type === 'love_at_first_sight') importance += 5;
        if (interaction.type === 'conflict') importance += 4;
        if (interaction.type === 'romantic_moment') importance += 4;
        
        return Math.min(importance, 10); // 最大重要性为10
    }
    
    /**
     * 更新角色关系
     */
    updateRelationships(interaction) {
        interaction.participants.forEach(participantId => {
            if (participantId !== this.id) {
                const currentRelation = this.relationships.get(participantId) || {
                    level: 0,
                    type: 'stranger',
                    history: []
                };
                
                // 根据交互类型更新关系
                this.adjustRelationship(currentRelation, interaction);
                
                // 记录关系历史
                currentRelation.history.push({
                    interaction: interaction.type,
                    emotion: interaction.emotion,
                    timestamp: new Date()
                });
                
                this.relationships.set(participantId, currentRelation);
            }
        });
    }
    
    /**
     * 调整关系等级
     */
    adjustRelationship(relation, interaction) {
        const adjustments = {
            'positive_interaction': 1,
            'romantic_moment': 2,
            'love_at_first_sight': 3,
            'conflict': -2,
            'awkward_moment': -1,
            'supportive_moment': 2
        };
        
        const adjustment = adjustments[interaction.type] || 0;
        relation.level = Math.max(-10, Math.min(10, relation.level + adjustment));
        
        // 更新关系类型
        if (relation.level >= 8) relation.type = 'love';
        else if (relation.level >= 6) relation.type = 'close_friend';
        else if (relation.level >= 3) relation.type = 'friend';
        else if (relation.level >= 0) relation.type = 'acquaintance';
        else relation.type = 'dislike';
    }
    
    /**
     * 更新个人成长
     */
    updateGrowth(interaction) {
        // 添加经历
        this.experiences.push({
            type: interaction.type,
            description: interaction.content,
            timestamp: new Date(),
            growth_impact: this.calculateGrowthImpact(interaction)
        });
        
        // 可能获得新的性格特质
        this.possiblyGainTrait(interaction);
        
        // 更新目标
        this.updateGoals(interaction);
    }
    
    /**
     * 计算成长影响
     */
    calculateGrowthImpact(interaction) {
        const impacts = {
            'love_at_first_sight': { confidence: 1, romantic: 2 },
            'conflict': { resilience: 1, communication: 1 },
            'supportive_moment': { empathy: 1, friendship: 1 },
            'awkward_moment': { humor: 1, adaptability: 1 }
        };
        
        return impacts[interaction.type] || { experience: 1 };
    }
    
    /**
     * 可能获得新特质
     */
    possiblyGainTrait(interaction) {
        const traitChances = {
            'romantic_moment': ['romantic', 'passionate'],
            'conflict': ['assertive', 'resilient'],
            'supportive_moment': ['caring', 'empathetic'],
            'funny_moment': ['humorous', 'cheerful']
        };
        
        const possibleTraits = traitChances[interaction.type];
        if (possibleTraits && Math.random() < 0.3) { // 30% 概率获得新特质
            const newTrait = possibleTraits[Math.floor(Math.random() * possibleTraits.length)];
            if (!this.traits.has(newTrait)) {
                this.traits.add(newTrait);
            }
        }
    }
    
    /**
     * 更新目标
     */
    updateGoals(interaction) {
        // 根据交互结果可能产生新目标
        if (interaction.type === 'love_at_first_sight') {
            this.goals.push(`与${interaction.participants.find(p => p !== this.id)}发展关系`);
        }
    }
    
    /**
     * 获取角色当前状态
     */
    getCurrentState() {
        return {
            id: this.id,
            name: this.name,
            emotion: this.currentEmotion,
            mood: this.mood,
            energy: this.energy,
            location: this.location,
            relationships: Object.fromEntries(this.relationships),
            traits: Array.from(this.traits),
            goals: this.goals,
            recentMemories: this.memory.getRecentMemories(3)
        };
    }
    
    /**
     * 生成角色的行为倾向
     */
    generateBehaviorTendency(situation, otherCharacters) {
        const tendency = {
            approach: 0.5, // 主动接近的倾向
            openness: 0.5, // 开放程度
            emotional_expression: 0.5, // 情感表达程度
            humor: 0.5, // 幽默倾向
            romantic: 0.5 // 浪漫倾向
        };
        
        // 根据性格调整
        this.personality.traits.forEach(trait => {
            switch(trait) {
                case 'outgoing':
                    tendency.approach += 0.2;
                    tendency.openness += 0.2;
                    break;
                case 'shy':
                    tendency.approach -= 0.2;
                    tendency.emotional_expression -= 0.1;
                    break;
                case 'romantic':
                    tendency.romantic += 0.3;
                    break;
                case 'humorous':
                    tendency.humor += 0.3;
                    break;
            }
        });
        
        // 根据当前情绪调整
        const emotionAdjustments = {
            'happy': { openness: 0.2, humor: 0.2 },
            'nervous': { approach: -0.2, emotional_expression: -0.1 },
            'excited': { approach: 0.3, emotional_expression: 0.2 },
            'sad': { approach: -0.1, openness: -0.2 }
        };
        
        const adjustment = emotionAdjustments[this.currentEmotion];
        if (adjustment) {
            Object.keys(adjustment).forEach(key => {
                tendency[key] += adjustment[key];
            });
        }
        
        // 确保值在0-1范围内
        Object.keys(tendency).forEach(key => {
            tendency[key] = Math.max(0, Math.min(1, tendency[key]));
        });
        
        return tendency;
    }
}

module.exports = Character;

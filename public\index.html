<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>斯坦福小镇 - 成长故事演示</title>
    <link rel="stylesheet" href="styles.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>🏫 斯坦福小镇 - 成长故事演示</h1>
            <div class="story-info">
                <span id="current-day">第 1 天</span>
                <span id="total-interactions">交互次数: 0</span>
                <button id="next-day-btn" class="btn btn-primary">推进到下一天</button>
                <button id="reset-btn" class="btn btn-secondary">重置故事</button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 角色面板 -->
            <section class="characters-panel">
                <h2>📱 角色状态</h2>
                <div class="characters-grid" id="characters-grid">
                    <!-- 角色卡片将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 交互历史 -->
            <section class="interactions-panel">
                <h2>💬 最近交互</h2>
                <div class="interactions-container" id="interactions-container">
                    <div class="no-interactions">暂无交互记录</div>
                </div>
            </section>

            <!-- 关系网络 -->
            <section class="relationships-panel">
                <h2>💕 关系网络</h2>
                <div class="relationships-container" id="relationships-container">
                    <!-- 关系图将通过JavaScript动态生成 -->
                </div>
            </section>
        </main>

        <!-- 控制面板 -->
        <aside class="control-panel">
            <h3>🎮 交互控制</h3>
            
            <div class="control-group">
                <label for="participant1">角色1:</label>
                <select id="participant1" class="form-control">
                    <option value="">选择角色</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="participant2">角色2:</label>
                <select id="participant2" class="form-control">
                    <option value="">选择角色</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="location">地点:</label>
                <select id="location" class="form-control">
                    <option value="">随机地点</option>
                    <option value="library">图书馆</option>
                    <option value="coffee_shop">咖啡厅</option>
                    <option value="campus">校园</option>
                    <option value="cafeteria">食堂</option>
                    <option value="park">公园</option>
                    <option value="art_gallery">美术馆</option>
                </select>
            </div>
            
            <button id="trigger-interaction" class="btn btn-primary btn-full">触发交互</button>
            
            <div class="auto-mode">
                <label>
                    <input type="checkbox" id="auto-mode" checked>
                    自动模式 (30秒一次随机交互)
                </label>
            </div>
        </aside>
    </div>

    <!-- 交互详情模态框 -->
    <div id="interaction-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>交互详情</h3>
            <div id="interaction-details"></div>
        </div>
    </div>

    <!-- 角色详情模态框 -->
    <div id="character-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 id="character-modal-title">角色详情</h3>
            <div id="character-details"></div>
        </div>
    </div>

    <!-- 记忆详情模态框 -->
    <div id="memory-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 id="memory-modal-title">记忆详情</h3>
            <div id="memory-details"></div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>系统初始化中...</p>
    </div>

    <!-- 通知容器 -->
    <div id="notifications" class="notifications"></div>

    <script src="app.js"></script>
</body>
</html>

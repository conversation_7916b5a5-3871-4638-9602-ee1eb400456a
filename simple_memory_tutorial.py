"""
简化版记忆系统教程 - 适合编程小白
一步一步教您如何实现记忆系统
"""

import time
from datetime import datetime

# 第一课：什么是类（Class）？
print("🎓 第一课：理解类（Class）")
print("=" * 40)
print("类就像一个模板，用来创建对象")
print("比如'汽车'是一个类，'我的红色本田'是一个对象")
print()

# 第二课：创建一个简单的记忆类
class SimpleMemory:
    """
    简单记忆类 - 代表一条记忆
    """
    def __init__(self, content, emotion="neutral"):
        """
        __init__ 是构造函数，创建对象时自动调用
        """
        self.content = content      # 记忆内容
        self.emotion = emotion      # 情绪
        self.strength = 1.0         # 记忆强度
        self.created_time = datetime.now()  # 创建时间
        
    def __str__(self):
        """
        __str__ 定义如何打印这个对象
        """
        return f"记忆: {self.content} (情绪: {self.emotion})"

print("🎓 第二课：创建记忆对象")
print("=" * 40)

# 创建一条记忆
memory1 = SimpleMemory("今天学会了编程", "happy")
print(f"创建了一条记忆: {memory1}")

memory2 = SimpleMemory("考试没考好", "sad")
print(f"创建了另一条记忆: {memory2}")
print()

# 第三课：创建记忆系统类
class SimpleMemorySystem:
    """
    简单记忆系统 - 管理多条记忆
    """
    def __init__(self, owner_name):
        """
        初始化记忆系统
        """
        self.owner_name = owner_name
        self.memories = []  # 用列表存储所有记忆
        
    def add_memory(self, content, emotion="neutral"):
        """
        添加新记忆
        """
        new_memory = SimpleMemory(content, emotion)
        self.memories.append(new_memory)
        print(f"✅ {self.owner_name} 添加了新记忆: {content}")
        return new_memory
    
    def get_all_memories(self):
        """
        获取所有记忆
        """
        return self.memories
    
    def search_memories(self, keyword):
        """
        搜索包含关键词的记忆
        """
        found_memories = []
        for memory in self.memories:
            if keyword in memory.content:
                found_memories.append(memory)
        return found_memories
    
    def get_memories_by_emotion(self, emotion):
        """
        按情绪获取记忆
        """
        emotion_memories = []
        for memory in self.memories:
            if memory.emotion == emotion:
                emotion_memories.append(memory)
        return emotion_memories
    
    def count_memories(self):
        """
        统计记忆数量
        """
        return len(self.memories)

print("🎓 第三课：使用记忆系统")
print("=" * 40)

# 创建王浩然的记忆系统
haoran_memory = SimpleMemorySystem("王浩然")
print(f"创建了 {haoran_memory.owner_name} 的记忆系统")
print()

# 添加一些记忆
print("📝 添加记忆...")
haoran_memory.add_memory("我是计算机研究生", "determined")
haoran_memory.add_memory("算法优化成功了", "proud")
haoran_memory.add_memory("遇到了陈晓晓", "curious")
haoran_memory.add_memory("和陈晓晓一起学习", "happy")
haoran_memory.add_memory("技术难题很困难", "frustrated")
print()

# 查看所有记忆
print("📚 所有记忆:")
all_memories = haoran_memory.get_all_memories()
for i, memory in enumerate(all_memories, 1):
    print(f"  {i}. {memory}")
print()

# 搜索记忆
print("🔍 搜索包含'陈晓晓'的记忆:")
search_results = haoran_memory.search_memories("陈晓晓")
for memory in search_results:
    print(f"  - {memory}")
print()

# 按情绪查找记忆
print("😊 查找开心的记忆:")
happy_memories = haoran_memory.get_memories_by_emotion("happy")
for memory in happy_memories:
    print(f"  - {memory}")
print()

# 统计信息
print(f"📊 总记忆数量: {haoran_memory.count_memories()}")
print()

# 第四课：更高级的功能
print("🎓 第四课：高级功能")
print("=" * 40)

class AdvancedMemory(SimpleMemory):
    """
    高级记忆类 - 继承自简单记忆类
    """
    def __init__(self, content, emotion="neutral", importance=5, participants=None):
        # 调用父类的构造函数
        super().__init__(content, emotion)
        
        # 添加新属性
        self.importance = importance  # 重要性 1-10
        self.participants = participants or []  # 参与者
        self.access_count = 0  # 访问次数
        
    def access(self):
        """
        访问记忆 - 增加访问次数
        """
        self.access_count += 1
        print(f"💭 回想起: {self.content} (已回想{self.access_count}次)")
        
    def decay(self):
        """
        记忆衰减
        """
        # 重要的记忆衰减慢一些
        decay_rate = 0.1 * (1 - self.importance / 10)
        self.strength = max(0.1, self.strength - decay_rate)
        
    def __str__(self):
        return f"记忆: {self.content} (重要性:{self.importance}, 强度:{self.strength:.2f})"

class AdvancedMemorySystem(SimpleMemorySystem):
    """
    高级记忆系统
    """
    def add_memory(self, content, emotion="neutral", importance=5, participants=None):
        """
        添加高级记忆
        """
        new_memory = AdvancedMemory(content, emotion, importance, participants)
        self.memories.append(new_memory)
        print(f"✅ {self.owner_name} 添加了新记忆: {content} (重要性:{importance})")
        return new_memory
    
    def get_important_memories(self, min_importance=7):
        """
        获取重要记忆
        """
        important = []
        for memory in self.memories:
            if hasattr(memory, 'importance') and memory.importance >= min_importance:
                important.append(memory)
        return important
    
    def simulate_time_passing(self):
        """
        模拟时间流逝 - 所有记忆都会衰减
        """
        print("⏰ 时间流逝，记忆开始衰减...")
        for memory in self.memories:
            if hasattr(memory, 'decay'):
                memory.decay()
    
    def get_memories_about_person(self, person_name):
        """
        获取关于某个人的记忆
        """
        person_memories = []
        for memory in self.memories:
            if hasattr(memory, 'participants') and person_name in memory.participants:
                person_memories.append(memory)
        return person_memories

# 演示高级功能
print("🚀 演示高级记忆系统:")
advanced_memory = AdvancedMemorySystem("王浩然")

# 添加高级记忆
advanced_memory.add_memory(
    "第一次见到陈晓晓", 
    emotion="curious", 
    importance=8, 
    participants=["陈晓晓"]
)

advanced_memory.add_memory(
    "和陈晓晓在图书馆学习", 
    emotion="happy", 
    importance=7, 
    participants=["陈晓晓"]
)

advanced_memory.add_memory(
    "完成了重要项目", 
    emotion="proud", 
    importance=9
)

print()

# 查看重要记忆
print("⭐ 重要记忆 (重要性≥7):")
important = advanced_memory.get_important_memories(7)
for memory in important:
    print(f"  - {memory}")
print()

# 查看关于陈晓晓的记忆
print("👩 关于陈晓晓的记忆:")
xiaoxiao_memories = advanced_memory.get_memories_about_person("陈晓晓")
for memory in xiaoxiao_memories:
    print(f"  - {memory}")
print()

# 访问记忆
print("🧠 访问记忆:")
if xiaoxiao_memories:
    xiaoxiao_memories[0].access()
    xiaoxiao_memories[0].access()  # 再次访问
print()

# 模拟时间流逝
print("⏳ 模拟时间流逝:")
advanced_memory.simulate_time_passing()
print("记忆衰减后:")
for memory in advanced_memory.get_all_memories():
    print(f"  - {memory}")
print()

print("🎉 教程完成！")
print("=" * 40)
print("您已经学会了:")
print("1. 如何创建类和对象")
print("2. 如何存储和管理记忆")
print("3. 如何搜索和筛选记忆")
print("4. 如何实现记忆的衰减和访问")
print("5. 如何处理复杂的记忆关系")
print()
print("💡 关键概念:")
print("- 类(Class): 对象的模板")
print("- 对象(Object): 类的实例")
print("- 方法(Method): 对象可以执行的操作")
print("- 属性(Attribute): 对象的特征")
print("- 继承(Inheritance): 子类继承父类的功能")

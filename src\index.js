const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const CharacterFactory = require('./characters/CharacterFactory');
const InteractionEngine = require('./core/InteractionEngine');

/**
 * 主应用类 - 斯坦福小镇成长故事系统
 */
class StorySystem {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server);
        
        this.characterFactory = new CharacterFactory();
        this.interactionEngine = null;
        this.isRunning = false;
        
        this.setupExpress();
        this.setupSocketIO();
    }
    
    /**
     * 设置Express服务器
     */
    setupExpress() {
        // 静态文件服务
        this.app.use(express.static(path.join(__dirname, '../public')));
        this.app.use(express.json());
        
        // 主页面
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '../public/index.html'));
        });
        
        // API路由
        this.setupAPIRoutes();
    }
    
    /**
     * 设置API路由
     */
    setupAPIRoutes() {
        // 获取所有角色信息
        this.app.get('/api/characters', (req, res) => {
            try {
                const characters = this.characterFactory.getAllCharacterInfo();
                res.json({ success: true, data: characters });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 获取特定角色信息
        this.app.get('/api/characters/:id', (req, res) => {
            try {
                const character = this.characterFactory.getCharacterInfo(req.params.id);
                if (character) {
                    res.json({ success: true, data: character });
                } else {
                    res.status(404).json({ success: false, error: 'Character not found' });
                }
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 获取角色记忆
        this.app.get('/api/characters/:id/memories', (req, res) => {
            try {
                const character = this.characterFactory.getCharacter(req.params.id);
                if (character) {
                    const memories = character.memory.getRecentMemories(10);
                    res.json({ success: true, data: memories });
                } else {
                    res.status(404).json({ success: false, error: 'Character not found' });
                }
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 获取关系网络
        this.app.get('/api/relationships', (req, res) => {
            try {
                const network = this.characterFactory.getRelationshipNetwork();
                res.json({ success: true, data: network });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 获取交互历史
        this.app.get('/api/interactions', (req, res) => {
            try {
                const limit = parseInt(req.query.limit) || 10;
                const history = this.interactionEngine ? 
                    this.interactionEngine.getInteractionHistory(limit) : [];
                res.json({ success: true, data: history });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 获取故事进度
        this.app.get('/api/story/progress', (req, res) => {
            try {
                const progress = this.interactionEngine ? 
                    this.interactionEngine.getStoryProgress() : null;
                res.json({ success: true, data: progress });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 触发新交互
        this.app.post('/api/interactions/trigger', async (req, res) => {
            try {
                const { participants, context } = req.body;
                
                if (!participants || participants.length < 2) {
                    return res.status(400).json({ 
                        success: false, 
                        error: 'At least 2 participants required' 
                    });
                }
                
                const interaction = await this.interactionEngine.startInteraction(
                    participants, 
                    context || {}
                );
                
                // 通过Socket.IO广播新交互
                this.io.emit('new_interaction', interaction);
                
                res.json({ success: true, data: interaction });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 推进故事到下一天
        this.app.post('/api/story/next-day', (req, res) => {
            try {
                if (this.interactionEngine) {
                    this.interactionEngine.advanceToNextDay();
                    const progress = this.interactionEngine.getStoryProgress();
                    
                    // 广播新的一天开始
                    this.io.emit('new_day', progress);
                    
                    res.json({ success: true, data: progress });
                } else {
                    res.status(400).json({ success: false, error: 'Story system not initialized' });
                }
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 重置故事系统
        this.app.post('/api/story/reset', async (req, res) => {
            try {
                await this.characterFactory.resetAllCharacters();
                this.interactionEngine = new InteractionEngine(this.characterFactory);
                
                // 广播重置事件
                this.io.emit('story_reset');
                
                res.json({ success: true, message: 'Story system reset successfully' });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
    }
    
    /**
     * 设置Socket.IO
     */
    setupSocketIO() {
        this.io.on('connection', (socket) => {
            console.log('Client connected:', socket.id);
            
            // 发送当前系统状态
            socket.emit('system_status', {
                isRunning: this.isRunning,
                characters: this.characterFactory.getAllCharacterInfo(),
                storyProgress: this.interactionEngine ? 
                    this.interactionEngine.getStoryProgress() : null
            });
            
            // 处理客户端请求
            socket.on('request_interaction', async (data) => {
                try {
                    const { participants, context } = data;
                    const interaction = await this.interactionEngine.startInteraction(
                        participants, 
                        context || {}
                    );
                    
                    // 广播新交互
                    this.io.emit('new_interaction', interaction);
                } catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            
            // 处理获取角色状态请求
            socket.on('get_character_state', (characterId) => {
                try {
                    const character = this.characterFactory.getCharacterInfo(characterId);
                    socket.emit('character_state', { characterId, data: character });
                } catch (error) {
                    socket.emit('error', { message: error.message });
                }
            });
            
            socket.on('disconnect', () => {
                console.log('Client disconnected:', socket.id);
            });
        });
    }
    
    /**
     * 初始化系统
     */
    async initialize() {
        try {
            console.log('Initializing Story System...');
            
            // 创建角色
            await this.characterFactory.createAllCharacters();
            console.log('Characters created successfully');
            
            // 初始化交互引擎
            this.interactionEngine = new InteractionEngine(this.characterFactory);
            console.log('Interaction engine initialized');
            
            this.isRunning = true;
            console.log('Story System initialized successfully!');
            
            // 开始自动故事推进
            this.startAutoStoryProgression();
            
        } catch (error) {
            console.error('Failed to initialize Story System:', error);
            throw error;
        }
    }
    
    /**
     * 开始自动故事推进
     */
    startAutoStoryProgression() {
        // 每30秒自动触发一次随机交互
        setInterval(async () => {
            try {
                if (this.isRunning && Math.random() < 0.7) { // 70%概率触发
                    await this.triggerRandomInteraction();
                }
            } catch (error) {
                console.error('Auto story progression error:', error);
            }
        }, 30000); // 30秒
        
        // 每5分钟推进一天
        setInterval(() => {
            try {
                if (this.isRunning) {
                    this.interactionEngine.advanceToNextDay();
                    this.io.emit('new_day', this.interactionEngine.getStoryProgress());
                    console.log(`Story advanced to day ${this.interactionEngine.storyProgress.day}`);
                }
            } catch (error) {
                console.error('Day advancement error:', error);
            }
        }, 300000); // 5分钟
    }
    
    /**
     * 触发随机交互
     */
    async triggerRandomInteraction() {
        const characters = this.characterFactory.getAllCharacters();
        
        // 随机选择2个角色
        const shuffled = characters.sort(() => 0.5 - Math.random());
        const participants = shuffled.slice(0, 2);
        
        // 随机选择场景
        const locations = ['library', 'coffee_shop', 'campus', 'cafeteria', 'park'];
        const location = locations[Math.floor(Math.random() * locations.length)];
        
        const interaction = await this.interactionEngine.startInteraction(
            participants.map(c => Object.keys(this.characterFactory.characters).find(key => 
                this.characterFactory.characters.get(key).id === c.id
            )),
            { location, auto_generated: true }
        );
        
        // 广播新交互
        this.io.emit('new_interaction', interaction);
        
        console.log(`Auto interaction: ${participants.map(p => p.name).join(' & ')} at ${location}`);
    }
    
    /**
     * 启动服务器
     */
    start(port = 3000) {
        return new Promise((resolve, reject) => {
            this.server.listen(port, (error) => {
                if (error) {
                    reject(error);
                } else {
                    console.log(`Story System server running on port ${port}`);
                    console.log(`Open http://localhost:${port} to view the story`);
                    resolve();
                }
            });
        });
    }
    
    /**
     * 停止系统
     */
    stop() {
        this.isRunning = false;
        this.server.close();
        console.log('Story System stopped');
    }
}

// 如果直接运行此文件，启动系统
if (require.main === module) {
    const storySystem = new StorySystem();
    
    storySystem.initialize()
        .then(() => storySystem.start(3000))
        .catch(error => {
            console.error('Failed to start Story System:', error);
            process.exit(1);
        });
    
    // 优雅关闭
    process.on('SIGINT', () => {
        console.log('\nShutting down Story System...');
        storySystem.stop();
        process.exit(0);
    });
}

module.exports = StorySystem;

/**
 * 简化的Web服务器版本 - 使用Node.js原生模块
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// 导入我们的测试系统组件
try {
    const { generateUUID, SimpleMemory, SimpleEmotionSystem, SimpleCharacter } = require('./test-components');
    global.generateUUID = generateUUID;
    global.SimpleMemory = SimpleMemory;
    global.SimpleEmotionSystem = SimpleEmotionSystem;
    global.SimpleCharacter = SimpleCharacter;
} catch (error) {
    console.error('无法加载测试组件，使用内联定义');
    // 内联定义基本组件
    global.generateUUID = function() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    };

    global.SimpleMemory = class {
        constructor(characterId) {
            this.characterId = characterId;
            this.memories = [];
        }

        async addMemory(memoryData) {
            const memory = { id: generateUUID(), ...memoryData, timestamp: new Date() };
            this.memories.push(memory);
            return memory.id;
        }

        async getRelevantMemories(query) {
            return this.memories.slice(-query.limit || 5);
        }

        getRecentMemories(limit = 5) {
            return this.memories.slice(-limit);
        }
    };

    global.SimpleEmotionSystem = class {
        constructor() {
            this.currentEmotion = 'neutral';
        }

        updateEmotionalState() {}
        processInteraction(interaction) {
            this.currentEmotion = interaction.emotion || 'happy';
        }
        getCurrentEmotion() { return this.currentEmotion; }
    };

    global.SimpleCharacter = class {
        constructor(name, gender, personality, background) {
            this.id = generateUUID();
            this.name = name;
            this.gender = gender;
            this.personality = personality;
            this.background = background;
            this.memory = new SimpleMemory(this.id);
            this.emotionSystem = new SimpleEmotionSystem();
            this.currentEmotion = 'neutral';
            this.energy = 100;
            this.location = 'home';
            this.relationships = new Map();
            this.traits = new Set(personality.traits || []);
            this.goals = ['寻找真爱', '个人成长'];
        }

        async prepareForInteraction(otherCharacter) {
            return { memories: [], emotion: this.currentEmotion, relationship: { level: 0, type: 'stranger' } };
        }

        async processInteractionResult(interaction) {
            await this.memory.addMemory(interaction);
            this.emotionSystem.processInteraction(interaction);
            this.currentEmotion = this.emotionSystem.getCurrentEmotion();

            // 更新关系
            interaction.participants.forEach(participantId => {
                if (participantId !== this.id) {
                    const currentRelation = this.relationships.get(participantId) || { level: 0, type: 'stranger' };
                    currentRelation.level += 1;
                    if (currentRelation.level >= 3) currentRelation.type = 'friend';
                    if (currentRelation.level >= 6) currentRelation.type = 'close_friend';
                    this.relationships.set(participantId, currentRelation);
                }
            });
        }

        getCurrentState() {
            return {
                id: this.id,
                name: this.name,
                emotion: this.currentEmotion,
                energy: this.energy,
                location: this.location,
                relationships: Object.fromEntries(this.relationships),
                traits: Array.from(this.traits),
                goals: this.goals,
                recentMemories: this.memory.getRecentMemories(3)
            };
        }
    };
}

class SimpleStoryServer {
    constructor() {
        this.characters = {};
        this.interactions = [];
        this.storyProgress = { day: 1, totalInteractions: 0 };
        
        this.initializeCharacters();
    }
    
    async initializeCharacters() {
        // 创建三个角色
        this.characters.xiayu = new global.SimpleCharacter(
            '林小雨',
            'female',
            { traits: ['gentle', 'introverted', 'artistic'] },
            '温柔内向的艺术系学生，喜欢画画和阅读'
        );
        
        this.characters.xiaoxiao = new global.SimpleCharacter(
            '陈晓晓',
            'female',
            { traits: ['outgoing', 'energetic', 'empathetic'] },
            '活泼开朗的心理学系学生，善于观察和理解他人'
        );

        this.characters.haoran = new global.SimpleCharacter(
            '王浩然',
            'male',
            { traits: ['intelligent', 'reliable', 'gentle'] },
            '聪明可靠的计算机科学研究生，理性而温和'
        );
        
        // 添加初始记忆
        await this.addInitialMemories();
        
        console.log('✅ 角色系统初始化完成');
    }
    
    async addInitialMemories() {
        await this.characters.xiayu.memory.addMemory({
            type: 'background',
            content: '我是一个喜欢画画的艺术系学生，希望能遇到理解我的人。',
            emotion: 'hopeful',
            importance: 8
        });
        
        await this.characters.xiaoxiao.memory.addMemory({
            type: 'background',
            content: '我是心理学系的学生，喜欢观察和理解人们的内心世界。',
            emotion: 'determined',
            importance: 8
        });
        
        await this.characters.haoran.memory.addMemory({
            type: 'background',
            content: '我是计算机科学的研究生，对技术充满热情，也希望找到生活的平衡。',
            emotion: 'thoughtful',
            importance: 8
        });
    }
    
    async triggerRandomInteraction() {
        const characterIds = Object.keys(this.characters);
        const shuffled = characterIds.sort(() => 0.5 - Math.random());
        const [char1Id, char2Id] = shuffled.slice(0, 2);
        
        const char1 = this.characters[char1Id];
        const char2 = this.characters[char2Id];
        
        // 准备交互
        await char1.prepareForInteraction(char2);
        await char2.prepareForInteraction(char1);
        
        // 生成交互
        const locations = ['图书馆', '咖啡厅', '校园', '食堂', '公园'];
        const location = locations[Math.floor(Math.random() * locations.length)];
        
        const interactionTypes = ['casual_chat', 'study_together', 'chance_encounter'];
        const type = interactionTypes[Math.floor(Math.random() * interactionTypes.length)];
        
        const dialogues = {
            xiayu: [
                '最近在画一幅新作品，灵感来自校园的梧桐树...',
                '你觉得艺术能表达出语言无法描述的情感吗？',
                '有时候我觉得，安静地坐着就是一种幸福。'
            ],
            xiaoxiao: [
                '今天心理学课上学到了很有趣的理论！',
                '你知道吗？人的第一印象其实很重要呢。',
                '我觉得了解一个人的内心世界是件很有意思的事。'
            ],
            haoran: [
                '最近在研究一个新的算法，虽然复杂但很有挑战性。',
                '技术发展得这么快，有时候觉得要不断学习才能跟上。',
                '你有什么兴趣爱好吗？我觉得工作之外也需要放松。'
            ]
        };
        
        const interaction = {
            id: global.generateUUID(),
            type: type,
            participants: [char1Id, char2Id],
            participantNames: [char1.name, char2.name],
            location: location,
            timestamp: new Date(),
            dialogue: [
                {
                    speaker: char1.name,
                    content: dialogues[char1Id][Math.floor(Math.random() * dialogues[char1Id].length)],
                    emotion: char1.currentEmotion
                },
                {
                    speaker: char2.name,
                    content: dialogues[char2Id][Math.floor(Math.random() * dialogues[char2Id].length)],
                    emotion: char2.currentEmotion
                }
            ]
        };
        
        // 处理交互结果
        await char1.processInteractionResult({
            id: interaction.id,
            type: interaction.type,
            content: `与${char2.name}在${location}${this.getTypeText(type)}`,
            participants: [char1.id, char2.id],
            emotion: 'happy'
        });
        
        await char2.processInteractionResult({
            id: interaction.id,
            type: interaction.type,
            content: `与${char1.name}在${location}${this.getTypeText(type)}`,
            participants: [char1.id, char2.id],
            emotion: 'happy'
        });
        
        this.interactions.unshift(interaction);
        this.storyProgress.totalInteractions++;
        
        // 保持交互历史在合理范围内
        if (this.interactions.length > 50) {
            this.interactions = this.interactions.slice(0, 50);
        }
        
        console.log(`🎭 新交互: ${char1.name} & ${char2.name} 在${location}`);
        
        return interaction;
    }
    
    getTypeText(type) {
        const typeTexts = {
            'casual_chat': '聊天',
            'study_together': '一起学习',
            'chance_encounter': '偶遇'
        };
        return typeTexts[type] || '互动';
    }
    
    getCharacterData() {
        const data = {};
        Object.keys(this.characters).forEach(id => {
            const char = this.characters[id];
            data[id] = {
                id: char.id,
                name: char.name,
                gender: char.gender,
                currentState: char.getCurrentState(),
                personality: char.personality,
                background: char.background
            };
        });
        return data;
    }
    
    getRelationshipData() {
        const data = {};
        Object.keys(this.characters).forEach(id => {
            const char = this.characters[id];
            data[id] = {};
            
            for (const [otherId, relationship] of char.relationships) {
                const otherChar = Object.values(this.characters).find(c => c.id === otherId);
                if (otherChar) {
                    const otherCharId = Object.keys(this.characters).find(key => 
                        this.characters[key].id === otherId
                    );
                    data[id][otherCharId] = {
                        name: otherChar.name,
                        level: relationship.level,
                        type: relationship.type
                    };
                }
            }
        });
        return data;
    }
    
    handleRequest(req, res) {
        const url = req.url;
        const method = req.method;
        
        // 设置CORS头
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        
        if (method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }
        
        if (url === '/' || url === '/index.html') {
            this.serveFile(res, 'public/index.html', 'text/html');
        } else if (url === '/styles.css') {
            this.serveFile(res, 'public/styles.css', 'text/css');
        } else if (url === '/app.js') {
            this.serveFile(res, 'public/simple-app.js', 'application/javascript');
        } else if (url === '/api/characters') {
            this.sendJSON(res, { success: true, data: this.getCharacterData() });
        } else if (url === '/api/relationships') {
            this.sendJSON(res, { success: true, data: this.getRelationshipData() });
        } else if (url === '/api/interactions') {
            this.sendJSON(res, { success: true, data: this.interactions.slice(0, 20) });
        } else if (url === '/api/story/progress') {
            this.sendJSON(res, { success: true, data: this.storyProgress });
        } else if (url === '/api/interactions/trigger' && method === 'POST') {
            this.triggerRandomInteraction().then(interaction => {
                this.sendJSON(res, { success: true, data: interaction });
            }).catch(error => {
                this.sendJSON(res, { success: false, error: error.message }, 500);
            });
        } else if (url === '/api/story/next-day' && method === 'POST') {
            this.storyProgress.day++;
            this.sendJSON(res, { success: true, data: this.storyProgress });
        } else {
            res.writeHead(404);
            res.end('Not Found');
        }
    }
    
    serveFile(res, filePath, contentType) {
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(404);
                res.end('File not found');
            } else {
                res.writeHead(200, { 'Content-Type': contentType });
                res.end(data);
            }
        });
    }
    
    sendJSON(res, data, statusCode = 200) {
        res.writeHead(statusCode, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(data));
    }
    
    start(port = 3000) {
        const server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });
        
        server.listen(port, () => {
            console.log(`🏫 斯坦福小镇成长故事系统启动成功！`);
            console.log(`🌐 请访问: http://localhost:${port}`);
            console.log(`📱 角色: ${Object.values(this.characters).map(c => c.name).join(', ')}`);
            console.log('');
            
            // 开始自动故事推进
            this.startAutoStory();
        });
        
        return server;
    }
    
    startAutoStory() {
        // 每30秒自动触发一次交互
        setInterval(() => {
            this.triggerRandomInteraction().catch(console.error);
        }, 30000);
        
        console.log('🤖 自动故事模式已启动 (每30秒一次随机交互)');
    }
}

// 启动服务器
if (require.main === module) {
    const server = new SimpleStoryServer();
    server.start(3000);
}

module.exports = SimpleStoryServer;
